/*
  This is a "Solution Style" tsconfig.json file, and is used by editors and TypeScript’s language server to improve development experience.
  It is not intended to be used to perform a compilation.

  To learn more about this file see: https://angular.io/config/solution-tsconfig.
*/
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ],
  "compilerOptions": {
    //"baseUrl": "./",
    "baseUrl": "src",
    "importHelpers": false,
    "target": "es6",
    "sourceMap": true, // enable source maps
    "paths": {
      "@src/*": ["src/*"]
      //"tslib" : ["path/to/node_modules/tslib/tslib.d.ts"]
    }
  },
  "angularCompilerOptions": {
    "strictTemplates": true
  }
  
}
