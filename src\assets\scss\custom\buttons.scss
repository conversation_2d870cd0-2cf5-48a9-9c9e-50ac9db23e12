@import "../lbd/buttons";

.btn-Secondary,
.btn-Minion,
.label-Secondary,
.label-Minion,
.btn-import {
  color: grey !important;
  i {
    color: grey !important;
  }
}

.btn-sm.middle {
  line-height: 30px;
}

.btn-import {
  @include btn-styles($colorful, $colorful);
}

.btn-Boss {
  @include btn-styles(#9062e4, #7d51cf);
}
.btn-NPC {
  @include btn-styles(#2bc08e, #1fa779);
}
.btn-None {
  @include btn-styles(#00ccff, #00b2df);
}
.btn-Subboss {
  @include btn-styles(#eb5f99, #d34a83);
}
.btn-Secondary {
  @include btn-styles(#e5e751, #cacc40);
}

.label-Boss {
  @include btn-styles(#909090, #9062e4);
}

.label-Boss {
  @include btn-styles(#9062e4, #9062e4);
}
.label-NPC {
  @include btn-styles(#2bc08e, #2bc08e);
}
.label-None {
  @include btn-styles(#00ccff, #00ccff);
}
.label-Subboss {
  @include btn-styles(#eb5f99, #eb5f99);
}
.label-Secondary {
  @include btn-styles(#e5e751, #e5e751);
}
.label-Minion {
  @include btn-styles(#e6e6e6, #e6e6e6);
}

.label-Boss,
.label-NPC,
.label-None,
.label-Subboss,
.label-Secondary,
.label-Minion {
  cursor: default;
}

.btn-Female {
  @include btn-styles(#dda0c4, #c988ae);
}
.btn-Male {
  @include btn-styles(#376fd8, #2961c8);
}
.btn-Unknown {
  @include btn-styles(#747474, #6e6e6e);
}
