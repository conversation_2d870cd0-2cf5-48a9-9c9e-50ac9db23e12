import {Ratio, Layout, AnchorPoint, PixelDimension} from 'src/models/mapsys1';
import { Position } from './map-editor.component';

export class Canvas {
    public dimension: PixelDimension;
    public canvasDimension: PixelDimension;
    public levelRadius: number;
    public context : CanvasRenderingContext2D;

    constructor(
        public el: HTMLCanvasElement,
        public deadZoneSize: number,
        levelDiameter: number,
        public ratio: Ratio,
    ) 
    {
        this.levelRadius = levelDiameter * 0.5;
        this.context = el?.getContext('2d');
    }

    get layout(): Layout 
    {
        return this.ratio.a > this.ratio.c ? 'vertical' : 'horizontal';
    }

    get deadZoneInPixels(): number 
    {
        return ((this.deadZoneSize * (this.layout === 'vertical' ? this.dimension.width : this.dimension.height)) * 0.01);
    }

    get circleRadiusInPixels(): number 
    {
        return (this.layout === 'vertical' ? this.dimension.width : this.dimension.height) * this.levelRadius * 0.01;
    }

    matchDimension(dimension: PixelDimension, noDeadZone?: boolean, layout?: Layout): PixelDimension 
    {
        const ratio = noDeadZone ? this.dimension.toRatio() : this.ratio;
        return ratio.toDimension((layout ? layout === 'vertical' : this.layout === 'vertical') ? 
        { width: dimension.width } : { height: dimension.height });
    }

    redimension(dimension: PixelDimension, setDeadZone: boolean, layout?: Layout): void 
    {
        this.dimension = this.ratio.toDimension((layout ? layout === this.layout : this.layout === 'horizontal') ? 
        { height: dimension.height } : { width: dimension.width });
        
        if (setDeadZone) 
        {
            this.canvasDimension = new PixelDimension(this.dimension.width + this.deadZoneInPixels * 2,
                 this.dimension.height + this.deadZoneInPixels * 2);
        } 
        else
        {
            this.canvasDimension = this.dimension;
        }
        this.el.style.width = this.canvasDimension.width + 'px';
        this.el.style.height = this.canvasDimension.height + 'px';

        this.el.width = this.canvasDimension.width;
        this.el.height = this.canvasDimension.height;
    }
    
    positionInPixels(position: AnchorPoint, withDeadZone?: boolean): Position 
    {
        if(!this.dimension.width) return;
        return {
            x: Math.floor(((withDeadZone ? this.canvasDimension.width : this.dimension.width) * position.xd) * 0.01),
            y: Math.floor(((withDeadZone ? this.canvasDimension.height : this.dimension.height) * position.yd) * 0.01),
        };
    }

    //This is based on old code that was not abstracted. It does the "same" as positionInPixelsForAnchorpoint but with deadzone
    positionInPixelsDeadzone(position: AnchorPoint): AnchorPoint 
    {
        let pos : Position = this.positionInPixels(position);
        return {
            xd: this.deadZoneInPixels + pos.x,
            yd: this.deadZoneInPixels + pos.y
        };
    }

    //This is based on old code that was not abstracted. It does the "same" as positionInPixelsForAnchorpoint but with deadzone
    positionInPixelsDeadzonePosition(position: Position): Position 
    {
        return {
            x: this.deadZoneInPixels + position.x,
            y: this.deadZoneInPixels + position.y
        };
    }

    //For more information go to documentation and look by the method name.
    positionInPixelsForAnchorpoint(position: AnchorPoint): AnchorPoint 
    {
        return {
            xd: (this.canvasDimension.width * position?.xd) / 100,
            yd: (this.canvasDimension.height * position?.yd) / 100,
        };
    }

    //same as the positionInPixelsForAnchorpoint method but returning a Position.
    positionInPixelsForPosition(position: AnchorPoint): Position 
    {
        return {
            x: (this.canvasDimension.width * position?.xd) / 100,
            y: (this.canvasDimension.height * position?.yd) / 100,
        };
    }

    //THis is inverse to the positionInPixelsForAnchorpoint. For more info look for: positionInPixelsForAnchorpoint in the documentation.
    fromPixelsToPercentage(position: AnchorPoint, withDeadZone?: boolean): AnchorPoint 
    {
        return {
            xd: Math.floor((withDeadZone ? (100*position.xd)/this.canvasDimension.width : (100*position.xd)/this.dimension.width)),
            yd: Math.floor((withDeadZone ? (100*position.yd)/this.canvasDimension.height : (100*position.yd)/this.dimension.height)),
        };
    }
  
}
