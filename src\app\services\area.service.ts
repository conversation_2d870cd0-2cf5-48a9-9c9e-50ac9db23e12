import { LevelService } from './level.service';
import { Injectable } from '@angular/core';
import { StaticService } from '../../templates/StaticService';
import { Area } from '../../models/mapsys1';

@Injectable({
  providedIn: 'root',
})
export class AreaService extends StaticService<Area> 
{
  unsortedLevels : string [] = [];
  runOnce : boolean = true;

  constructor(private _levelService: LevelService) 
  {
    super(Area.deepLoad, 'Area');
  }
  public sortLevels(areaId: string): void 
  {
    const area = this.findById(areaId);
    const levels = this._levelService.filterByIds(area.levelIds);
    area.levelIds = [];
    levels.forEach((level) => 
    {
      area.levelIds.push(level.id);
    });
    this.save();
  }

  public getLevelIndex(levelId: string): number 
  {
    if(this.runOnce)
    {
      this.runOnce = false;
      this.sortLevelArray(levelId);
    }

    let index = 0;
    for(let i = 0; i < this.unsortedLevels.length; i++)
    {
      if(this.unsortedLevels[i] == levelId)
      {
        index = i;
        break;
      }
    }
    return index;
  }

  sortLevelArray(levelId: string)
  {
    this.unsortedLevels = [];
    let splitId = levelId.split('.');
    
    let area = this.findById(splitId[0]);
    for(let i = 0; i < area.levelIds.length; i++)
    {
      let level = area.levelIds[i];
      this.unsortedLevels.push(level);
    }

    this.unsortedLevels = area.levelIds;
  }
}
