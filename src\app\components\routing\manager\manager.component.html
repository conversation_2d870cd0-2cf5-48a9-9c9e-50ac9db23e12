<div class="content">
  <div class="container-fluid">
    <div class="col-md-12">
      <div class="card">
        <table class="table table-responsive table-list">
          <thead style="top: 0px">
            <tr>
              <th>ID</th>
              <th>Hierarchy code</th>
              <th>Name</th>
              <th>Description</th>
              <th>Notes</th>
              <th>Levels</th>
              <th>Maps</th>
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="let area of areas; let i = index; trackBy: trackByIndex"
            >
              <tr id="{{ area.id }}">
                <td class="category">{{ area.id }}</td>
                <td class="td-id">
                  {{ area.hierarchyCode }}
                </td>
                <td>
                  <p>{{ area.name }}</p>
                </td>
                <td class="td-relative">
                  <p>{{ area.description }}</p>
                </td>
                <td class="td-relative">
                  <p>{{ area.notes }}</p>
                </td>
                <td>
                  <p>{{ area.levelIds.length }}</p>
                </td>
                <td class="td-actions">
                  <button
                    *ngIf="(area.id | mapsFromArea).length === 0"
                    class="btn btn-sm btn-success btn-fill btn-remove"
                    (click)="newAddMap(area.id)"
                  >
                    <i class="pe-7s-plus"></i>
                  </button>

                  <ng-container *ngFor="let map of area.id | mapsFromArea">
                    <tr class="no-top-border" id="{{ map.id }}">
                      <td class="td-actions">
                        <button
                          class="btn btn-sm btn-danger btn-fill btn-remove"
                          (click)="promptRemoveMap(map)"
                        >
                          <i class="pe-7s-close"></i>
                        </button>
                      </td>
                      <td class="id">{{ map.id }}</td>
                      <td class="td-relative">
                        <i
                          *ngIf="
                            (map | levelsMapped: area.levelIds) !==
                              area.levelIds.length || (area | MapTextMapped) !== 0;
                            else noErrors
                          "
                          title="Not all levels mapped"
                          class="pe-7s-attention warning"
                        ></i>
                        <ng-template #noErrors
                          ><i class="pe-7s-check success"></i
                        ></ng-template>
                      </td>
                      <td>
                        <p>{{ map | levelsMapped: area.levelIds }}</p>
                      </td>
                      <td class="td-actions">
                        <button
                          class="btn btn-info btn-fill"
                          (click)="editMap(map.id)"
                        >
                          <i class="pe-7s-map-2"></i>
                        </button>
                      </td>
                    </tr>
                  </ng-container>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
