
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ImageRedimensionService 
{
    deviceDiagonalInches: number = 0;
    deviceDiagonalCM: number = 0;
    deviceDiagonal: number = 1;
    devicePPI: number = 1;
    desiredPPI: number = 0;

    constructor(){}
  
    //PPI = pixelDiagonal / inchesDiagonal
    calculateScreenPPI(deviceDiagonal: number) : number
    {
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;
      const pixelDiaganol = Math.sqrt(Math.pow(screenWidth, 2) + Math.pow(screenHeight, 2));
  
      const PPI = pixelDiaganol / deviceDiagonal;
      this.devicePPI = PPI;
      return PPI;
    }

  
    //inch = imageWidth / PPI
    calculateCurrentImageWidthInch(currentImageWidth: number): number
    {
      const inch = currentImageWidth / this.devicePPI;
      return inch;
    }

    //inch = imageHeight / PPI
    calculateCurrentImageHeightInch(currentImageHeight: number): number
    {
      const inch = currentImageHeight / this.devicePPI;
      return inch;
    }

    //inch = imageWidth / PPI
    calculateDesiredImageWidthInch(currentImageWidth: number): number
    {
      const inch = currentImageWidth / this.desiredPPI;
      return inch;
    }

    //inch = imageHeight / PPI
    calculateDesiredImageHeightInch(currentImageHeight: number): number
    {
      const inch = currentImageHeight / this.desiredPPI;
      return inch;
    }

    //newPixelWidth = (currentWidthInch / desiredWidthInch) * currentPixelWidth.
    desiredImageWidth(currentImageWidth: number): number
    {
        let ratio = this.calculateCurrentImageWidthInch(currentImageWidth) / this.calculateDesiredImageWidthInch(currentImageWidth);
        
        let newImageWidth = currentImageWidth * ratio;

        return newImageWidth;
    }

    //newPixelHeight = (currentHeightInch / desiredHeightInch) * currentPixelHeight.
    desiredImageHeight(currentImageHeight: number): number
    {
        let ratio = this.calculateCurrentImageHeightInch(currentImageHeight) / this.calculateDesiredImageHeightInch(currentImageHeight);
        
        let newImageHeight = ratio / currentImageHeight;

        return newImageHeight;
    }
}