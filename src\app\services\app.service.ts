import { Injectable } from '@angular/core';
import { Version } from 'src/templates/Versioning';
import { Subject } from 'rxjs';
import { getData, setData } from 'src/custom/others';
import { File } from 'src/custom/File';

@Injectable({
  providedIn: 'root',
})
export class AppService 
{
  typeName = 'App';
  appVersion: Version = new Version(1, 4, 4);
  build = '20220207_1010';
  data: 
  {
    DSA_lastLoadedTime?: Date;
    MPS_lastLoadedTime?: Date;
  };
  lastDSALoadedTimeSubject: Subject<Date> = new Subject<Date>();
  lastMPSLoadedTimeSubject: Subject<Date> = new Subject<Date>();

  constructor() 
  {
    this.loadData();
  }

  setDSALastLoadedTime(value: Date): void 
  {
    this.data.DSA_lastLoadedTime = value;
    this.lastDSALoadedTimeSubject.next(this.data.DSA_lastLoadedTime);
    this.saveData();
  }

  setMPSLastLoadedTime(value: Date): void 
  {
    this.data.MPS_lastLoadedTime = value;
    this.lastMPSLoadedTimeSubject.next(this.data.MPS_lastLoadedTime);
    this.saveData();
  }

  private loadData(): void 
  {
    this.data = getData(this.typeName, File.MPS_SUFFIX_PATH) || {};
    this.saveData();
  }

  private saveData(): void 
  {
    setData(this.data, this.typeName, File.MPS_SUFFIX_PATH);
  }
}
