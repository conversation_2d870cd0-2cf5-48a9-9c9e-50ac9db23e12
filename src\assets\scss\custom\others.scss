.input-file {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.circle {
  z-index: 2;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 0px solid #555;
  background-color: white;
  color: #555;
  line-height: 100px;
  position: relative;
}

.manager-battle-characters-td {
  width: inherit;
}

.sidebar-footer {
  position: absolute;
  font-size: 13px;
  text-align: center;
  bottom: 10px;
  left: 25px;
  z-index: 10;
  opacity: 0.5;
}

.relative {
  position: relative !important;
}

.table-box {
  > tbody > tr:hover {
    background-color: rgba(140, 132, 158, 0);
  }
}

.btn-invert {
  color: white !important;
}
.btn-invert.btn-danger:hover {
  background-color: red;
}
.btn-invert.btn-success:hover {
  background-color: #87cb16;
}

.td-actions {
  .btn-remove {
    opacity: 0.36 !important;
  }
  .btn-remove:hover {
    opacity: 1 !important;
  }
}

.character-absolute-tabs {
  position: absolute !important;
  // top: 90px;
  top: 0px;
  left: 30%;
}

.level-header-absolute-table {
  position: absolute !important;
  top: -20px;
  left: 30%;
}

.no-dialog-button {
  opacity: 0;
}

.no-dialog-button:hover {
  border-color: #1dc7ea;
  color: #1dc7ea;
}

.no-error-check {
  position: absolute;
  font-size: 200px;
  color: #bada55;
  top: 42%;
  right: 42%;
}

.add-button {
  position: absolute;
  top: 20px;
  right: 30px;
}

.add-buttons {
  position: absolute;
  /* top: 10px;
  right: 13px; */
  top: -70px;
  right: 13px;
}

.back-button {
  left: 23px;
}

.clipboard-button {
  position: absolute;
  top: -70px;
  left: 100px;
}

.editor-title {
  position: relative;
  top: -10px;
  left: 223px;
}

//special classes
.th-clickable:hover {
  background-color: rgb(194, 194, 194);
  cursor: pointer;
}

.table-clickable {
  tr:hover {
    background-color: rgb(229, 231, 233);
    cursor: pointer;
  }
}

.td-clickable:hover {
  background-color: rgb(194, 194, 194);
  cursor: pointer;
}

.td-buttons {
  width: 20px;
}

.relative {
  position: relative;
  min-width: 1%;
}

.height-90vh {
  height: 90vh;
}
.height-45pc {
  height: 42%;
}
.height-50pc {
  height: 48%;
}
.height-90pc {
  height: 90%;
}
.height-100pc {
  height: 100%;
}

.collumn {
  width: auto;
  position: relative !important;
  float: center !important;
  margin: 10px;
}

.collumn::after {
  clear: both !important;
  display: table !important;
}

p i {
  font-size: 75px;
  vertical-align: middle;
  display: inline-block;
}

.form-control {
  height: auto;
}

.form-100 {
  width: 100%;
}

.td-80 {
  width: 80%;
}

.width-30pc {
  width: 30% !important;
}

.width-50pc {
  width: 50% !important;
}

.width-100pc {
  width: 100% !important;
}

.width-max-300px {
  max-width: 100%;
  min-height: 100% !important;
}

.pipe-table {
  font-size: 12px;
  position: relative;
  width: auto;
  > tbody {
    display: block;
    max-width: 250px;
    height: 100px !important;
    overflow: auto;
  }
  > tbody > tr > td > i {
    font-size: 15px !important;
  }
}

.no-button {
  opacity: 0 !important;
}
.no-button:hover {
  opacity: 0.8 !important;
}

.table-clickable {
  > tbody > tr:hover {
    background-color: rgb(194, 194, 194);
    cursor: pointer;
  }
}

.form-wide {
  display: inline-block;
  margin: 10px auto;
  width: 250px;
  height: 100px;
}

.form-short {
  margin: 10px auto;
  //display: inline-block;
  width: 250px;
  height: auto;
  //vertical-align: middle;
}

.form-title {
  display: inline-block;
  vertical-align: middle;
  font-size: xx-large;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  width: 700px;
}
.form-title:hover {
  background-color: rgba(255, 255, 255, 1);
}
.form-title.large {
  font-size: large;
}
.form-title.no-white:hover {
  background-color: rgba(255, 255, 255, 0);
}

.special-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: inherit;
}

.left {
  text-align: left;
}

.center {
  margin-left: auto;
  margin-right: auto;
}

.vertical-align {
  margin: auto 20px !important;
}

.middle {
  margin-left: auto;
  margin-right: auto;
  margin-top: auto;
  margin-bottom: auto;
  vertical-align: middle;
  align-content: center;
  text-align: center;
}

.notification-circle {
  border-radius: 50%;
  margin: 0;
  display: inline-block;
  vertical-align: middle;
}

.notification-circle.large {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  margin: 5px;
  display: inline-block;
  vertical-align: middle;
}

.not {
  position: relative;
  width: 100%;
  right: -1% !important;
}

.not-circle {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  margin: 5px;
  position: absolute;
  right: 130px;
  transform: translateY(-50%);
}

.not-circle-speakers {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  margin: 5px;
  position: absolute;
  left: -20px;
  top: -25px;
}

.notification-circle.small {
  height: 12px;
  width: 12px;
}

.logo > i {
  display: block;
  font-size: 75px;
  margin: 10px auto;
}

.icon {
  display: block;
  font-size: 75px;
  //margin: 10px auto;
}

.icon-review {
  display: inline-block;
  vertical-align: middle;
  font-size: 36px;
}

.icon.title {
  display: inline-block;
  vertical-align: middle;
}

.icon.center {
  margin: 30px;
  vertical-align: middle;
}

.anchor {
  scroll-margin-top: 300px;
}

.list-header-row {
  width: auto;
  top: 0px;
  height: auto;
  margin-bottom: 20px;
  z-index: 50;
  background-color: white;
  position: sticky !important;
}

i.icon-large {
  font-size: 100px !important;
  display: block;
  text-align: right;
}

i.icon-small {
  font-size: 20px !important;
}

tbody tr {
  padding-top: 400px;
  margin-top: 5em;
  offset-anchor: auto;
}

.report-table,
.report-table.full-width {
  width: 100%;
  font-size: 16px;
  display: block;
  overflow: auto;
  tbody,
  tr {
    width: 100% !important;
  }
  td {
    //width: 1%;
    //min-width: 1%;
    min-width: 130px;
    width: inherit;
  }
  td.td-auto {
    width: auto;
  }
  i {
    font-size: 20px !important;
  }
  .btn-icon {
    font-size: 50px !important;
  }
}

.small-table {
  font-size: 12px;
  position: relative;
  > tbody {
    display: block;
    height: 500px;
    overflow: auto;
  }
  > tbody > tr > td > i {
    font-size: 20px !important;
  }
}

.btn-icon {
  font-size: 50px !important;
}

.small-table.half {
  width: 100px;
}

td.td-100 {
  width: 100% !important;
}

td.td-8 {
  width: 8% !important;
}

td.td-20 {
  width: 20% !important;
}

td.td-50 {
  width: 70% !important;
}

i.info,
i.peace,
i.success,
i.marker,
i.warning,
i.error,
i.attention {
  font-size: 36px;
}

.red-eye {
  color: red;
}

.info {
  color: #1dc7ea;
}

.peace {
  color: darkcyan;
}
.success {
  color: #28a745;
}
.marker {
  color: #3472f7;
}
.warning {
  color: #ff9500;
}
.error {
  color: red;
}
.attention {
  color: orangered;
}

.bkg-peace {
  background-color: darkcyan;
}
.bkg-success {
  background-color: #28a745;
}
.bkg-marker {
  background-color: #4d83d7;
}
.bkg-warning {
  background-color: #ff9500;
}
.bkg-error {
  background-color: red;
}
.bkg-attention {
  background-color: orangered;
}

.row-responsive {
  width: 92vw;
}

button > i {
  font-size: 40px;
}

button.row-btn > i {
  font-size: 20px;
}

.row > button {
  margin: 10px;
  float: right;
}

td > .row {
  text-align: center;
}

td.row {
  display: flex;
}

.anchor.review {
  background-color: lightpink !important;
  margin: 20px;
  border: 20px;
  border-color: red;
}

.link-list {
  width: 200px !important;
}
.link-list-problem {
  width: 200px !important;
  background-color: rgb(255, 220, 226) !important;
  text-align: center;
  margin: 20px;
  border: 20px;
  border-color: red;
}

.td-id {
  color: rgb(184, 184, 184) !important;
}

.btn-pink {
  background-color: rgb(221, 160, 196) !important;
  border-color: rgb(221, 160, 196) !important;
}
.btn-pink:hover {
  background-color: rgb(201, 136, 174) !important;
  border-color: rgb(201, 136, 174) !important;
}

.table-scrollable {
  overflow: auto;
  height: 100px;
}

.filter-dropdown {
  display: block;
  //display: inline;
  //width: 100%;
  //height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-dropdown.auto {
  width: auto;
}

.filter-dropdown.limited {
  max-width: 300px;
}

.td-small {
  width: 20px !important;
  text-align: center;
}

.td-medium {
  width: 300px;
  text-align: center;
}

.td-100px {
  width: 100px;
}

.btn-sm {
  i {
    font-size: 30px;
  }
}

.btn-hidden {
  opacity: 0 !important;
}
.btn-hidden:hover {
  opacity: 1 !important;
}

.btn-linkedLevel {
  position: absolute;
  z-index: 30;
  left: 40px;
  top: -7px;
  i {
    font-size: 30px;
  }
}

.btn-75px {
  width: 75px;
}

.btn-link {
  position: absolute;
  z-index: 30;
  transform: translateX(-30%);
  opacity: 0;
  i {
    font-size: 30px;
  }
}
.btn-link:hover {
  opacity: 1;
}

.form-link {
  float: left;
  width: 50px !important;
  border-width: 1px;
  padding: 1px;
}

.form-id {
  width: 100px !important;
}

.td-200px {
  width: 200px;
}
.td-min-200px {
  min-width: 200px !important;
}

.storybox-row {
  display: inherit !important;
}

.speech-table {
  background-color: rgba(0, 0, 0, 0) !important;
  width: auto !important;
  th,
  td,
  label {
    color: rgb(0, 0, 0) !important;
  }
}

.event-table {
  background-color: #f9ad42 !important;
  width: auto !important;
  th,
  td,
  label {
    color: white !important;
  }
}
.box-title-watermark {
  position: absolute;
  left: 30vw;
  top: 30px;
  color: light;
  font-size: 50px;
  text-align: center;
}
.marker-table {
  background-color: #4d83d7 !important;
  width: auto !important;
  th,
  td,
  label {
    color: white !important;
  }
}

.card.list-header {
  height: 250px;
}

.sticky {
  position: sticky !important;
  //top: 250px;
  background-color: #e6e6e6;
  z-index: 40;
}

//list css
.table-list {
  text-align: center;
  > thead th {
    text-align: center;
  }
  //make the table header sticky
  > thead {
    position: sticky !important;
    //top: 250px;
    background-color: $dark;
    z-index: 40;
  }
  //table row height
  > tbody > tr {
    height: auto !important;
  }
  > tbody > tr > td {
    height: 100% !important;
  }
  .td-sort {
    background-color: #e6e6e6;
  }
  //no paddng for any type of table element
  //most squished as possible
  td {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
  .td-relative {
    position: relative;
    height: 100%;
    width: 50%;
  }
  .td-auto {
    //to replace while compiling
    //width: auto;
    //to replace when building
    width: 1%;
  }

  .objective-star {
    z-index: 2;
    text-align: center;
    width: 50px;
    height: 50px;
    font-size: 20px;
    line-height: 100px;
    top: -7px;
    i {
      transform: translateX(-30%);
      position: absolute;
      font-size: 50px;
    }
    .text {
      transform: translateY(-22%) translateX(300%);
      position: absolute;
      font-size: 18px;
    }
  }

  //leveling td
  td.branching-td {
    position: relative;
    .branching {
      position: absolute;
      height: 100%;
      top: 0;
      .branch-line {
        width: 3px;
        height: 50%;
        display: flex;
        border: 3px solid #bada55;
        background-color: #bada55;
        position: absolute;
        left: 47px;
      }
      .branch-line.child {
        top: 0;
      }
      .branch-line.parent {
        bottom: 0;
      }
      .branch-line.no-links {
        opacity: 0;
      }
      .circle {
        z-index: 2;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 3px solid #bada55;
        font-size: 25px;
        background-color: white;
        color: #bada55;
        line-height: 100px;
        position: absolute;
      }
      .circle.branch-circle {
        top: 50%;
        transform: translateY(-50%);
        .circle.branch-error-circle {
          z-index: 3;
          width: 30px;
          height: 30px;
          color: black;
          font-size: 20px;
          line-height: 24px;
          bottom: 33px;
          left: 33px;
          color: white;
          border: 3px solid red;
          background-color: red;
        }
        .circle.branch-error-circle.to-other-area-circle {
          border: 3px solid purple !important;
          background-color: purple !important;
        }
        .circle.branch-error-circle.unlocked-branch {
          background-color: rgb(255, 226, 60);
          top: 50%;
          right: 0;
          transform: translateY(-150%);
          bottom: 60px;
          left: 60px;
          color: black;
        }
        .circle.branch-error-circle.no-child-links {
          top: 78px;
        }
        .circle.branch-error-circle.no-parent-links {
          top: -12px;
        }
        .fullLevelId {
          z-index: 3;
          width: 90px;
          height: 20px;
          color: black;
          font-size: 20px;
          line-height: 24px;
          top: 37px;
          right: 2px;
          background-color: red;
          color: white;

          background-color: white;
          position: absolute;
          height: auto;
          font-size: 12px;
          opacity: 0;
        }
        .fullLevelId:hover {
          // display: block;
          opacity: 1;
        }
      }
    }
  }
  > tbody > tr {
    //all tables inside the row
    > td {
      table {
        text-align: left;
        // width: auto;
        height: 50px;
        margin-bottom: 0;
        background-color: #000;
        tr,
        thead,
        th,
        .btn {
          padding-top: 0;
          padding-bottom: 0;
        }
        i {
          font-size: 25px;
        }
        //absolute overlay
        .absolute-wrap {
          @include opacity(0);
          height: 100%;
          position: absolute;
          width: 25px;
          top: 0;
          z-index: 1;
          .btn-absolute {
            left: 50%;
            height: 100%;
            position: relative;
            i {
              font-size: 50px;
            }
          }
        }
        .td-focus:hover .absolute-wrap {
          @include opacity(1);
        }
      }
      .btn-group.absolute-btn-group:hover {
        @include opacity(1);
      }
      .btn-group.absolute-btn-group {
        @include opacity(0);
        z-index: 30;
        top: 100%;
        transform: translateY(-50%);
        position: absolute;
        width: 200px;
        .btn {
          position: relative;
        }
      }
      .btn-group.absolute-btn-group:hover {
        @include opacity(1);
      }
      .td-focus,
      .tr-focus {
        .tr-btn-focus,
        .td-btn-focus {
          @include opacity(0);
          z-index: 30;
        }
      }
      .td-focus:hover {
        .td-btn-focus {
          @include opacity(1);
        }
      }
      .tr-focus:hover {
        .tr-btn-focus {
          @include opacity(1);
        }
      }
      .absolute-wrap {
        position: absolute;
        height: 100%;
        top: 0;
      }
    }
    .absolute-wrap {
      position: absolute;
      top: 0;
      min-height: 100%;
      width: 100%;
      white-space: nowrap;
    }
    .forms-wrapper {
      position: relative;
      min-width: 100px;
      padding: 5px;
      min-height: 100px;
      height: 100%;
    }
    .form-control {
      width: 100%;
      max-width: 100%;
      min-height: 100% !important;
      height: 120px;
      resize: none;
      overflow: auto;
      position: relative;
      margin: 0;
    }
    .form-control.borderless {
      border: 0;
    }
    .form-control.form.short {
      height: 30px;
    }
    select {
      display: flexbox;
      left: 0;
      text-align: left !important;
      margin-left: 0;
    }
    .form-short {
      height: 30px;
    }
    .form-control.auto {
      width: auto;
    }
    .td-highlight .form-control {
      background-color: #c9dee0;
    }
    .collumn {
      width: auto;
      float: left;
    }
    .collumn::after {
      clear: both;
      display: table;
    }
  }
}

.battle-character-table,
.compact-battle-character-table {
  background-color: transparent !important;
  tr,
  td,
  thead,
  th {
    height: auto !important;
    padding: 0 !important;
  }
  i {
    font-size: 30px;
  }
}

.compact-battle-character-table {
  button {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

.no-horizontal-margin {
  margin-right: 0px !important;
  margin-left: 0px !important;
}

.img-icon-button {
  width: 60px;
  margin-left: 15px;
  justify-content: center;
  align-items: center;
  display: flex;
  img {
    //width: 40px;
    scale: 1.5;
  }
}

.td-20px {
  width: 20px !important;
  min-width: max-content !important;
  text-align: center;
}
.td-min {
  width: 1px !important;
  min-width: max-content !important;
  text-align: center;
}

.td-sort {
  width: 10px !important;
  text-align: center;
}
.td-sort {
  .compact {
    min-width: max-content !important;
    display: block;
    // flex-direction: column;
  }
}

.table-hover {
  tr:hover {
    background-color: $medium;
    cursor: pointer;
  }
}

.card label.btn {
  margin-bottom: 0px;
}

table.table-storage {
  .td-kb {
    text-align: right;
    width: auto;
  }
}

.btn-table {
  td,
  button {
    width: 50px;
    height: 50px;
  }
  p {
    line-height: 2.7;
    font-size: 13px;
    border: 1px white;
    border-radius: 1;
  }
}

tbody {
  background-color: $dark;
}

tr.no-top-border {
  background-color: $darkest;
  td {
    border-top: 0;
  }
}

.card {
  border: 1px solid rgba(0, 0, 0, 0) !important;
}
