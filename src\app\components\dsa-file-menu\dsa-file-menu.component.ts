import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Alert } from 'src/custom/Alert';
import { File } from 'src/custom/File';
import { AppService } from 'src/app/services/app.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Subscription } from 'rxjs';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-dsa-file-menu',
  templateUrl: './dsa-file-menu.component.html',
  styleUrls: ['./dsa-file-menu.component.scss'],
})
export class DSAFileMenuComponent implements OnInit, OnDestroy {
  showImportDSAButton: boolean;
  preferencesSubscription: Subscription;
  appSubscription: Subscription;
  lastLoadedTime: Date;

  constructor(
    private _appService: AppService,
    private _userSettingsService: UserSettingsService,
    private _dataService: DataService
  ) {}

  ngOnInit(): void {
    this.showImportDSAButton = !this._userSettingsService.data
      .detectDSADataFromLocalStorage;
    this.preferencesSubscription = this._userSettingsService.lastPreferencesChange.subscribe(
      () => {
        this.showImportDSAButton = !this._userSettingsService.data
          .detectDSADataFromLocalStorage;
      }
    );
    this.appSubscription = this._appService.lastDSALoadedTimeSubject.subscribe(date => this.lastLoadedTime = date);
  }
  ngOnDestroy(): void {
    this.preferencesSubscription.unsubscribe();
    this.appSubscription.unsubscribe();
  }

  fileReader = new FileReader();
  promptDSAFile(files: Blob[]): void 
  {
    this.fileReader = new FileReader();
    this.fileReader.onload = (error) => 
    {
      this.loadDSA(this.fileReader.result);
    };
    this.fileReader.readAsText(files[0]);
  }

  
  private loadDSA(json): void 
  {
    let dsa = new File.DSA(JSON.parse(json));
    
    let newError = dsa.validationCheck();
    if(newError)
    {
      Alert.showError(newError);
      return;
    }
    this._dataService.importDsa(dsa);
    Alert.showSuccess('Data imported', '<h6>Version</h6>' + dsa.fileFormat, undefined, 'top-end' );
  }
}
