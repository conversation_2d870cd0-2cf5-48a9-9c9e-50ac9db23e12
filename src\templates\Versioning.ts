export class Version {
  constructor(public major: number, public minor: number, public patch: number) { }
  tostring(): string { return this.major + '.' + this.minor + '.' + this.patch; }
}

export abstract class Versioning {
  public getCompatibleVersion(): string { return this._appVersion + '_' + this._exportedDateTime; }

  constructor(
    protected _exportedDateTime?: string,
    protected _appVersion?: string) {
  }

  public isHigherThan(ver: Version): boolean {
    const split = this._appVersion.split('.');
    const appVersion = new Version(
      parseInt(split[0], 10),
      parseInt(split[1], 10),
      parseInt(split[2], 10)
    );
    return appVersion.major >= ver.major
      && ((appVersion.minor === ver.minor
        && appVersion.patch >= ver.patch) ||
        (appVersion.minor > ver.minor)) ? true : false;
  }

  public isCompatible(obj: Versioning): boolean {
    return this.getCompatibleVersion() === obj.getCompatibleVersion();
  }
}
