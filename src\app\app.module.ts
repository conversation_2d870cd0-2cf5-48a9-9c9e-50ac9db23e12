import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';

import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module';
import { SidebarModule } from './components/sidebar/sidebar.module';

import { AppComponent } from './app.component';
import { PopupComponent } from './components/popup/popup.component';
import { MapEditorComponent } from './components/routing/map-editor/map-editor.component';
import { ManagerComponent } from './components/routing/manager/manager.component';
import { MapEditorStylePipe } from './pipes/map-editor-style.pipe';
import { MapsFromAreaPipe } from './pipes/maps-from-area.pipe';
import { LevelsMappedPipe } from './pipes/levels-mapped.pipe';
import { MapTextMappedPipe } from './pipes/map-text-mapped.pipe';
import { IncludedInMapStylePipe } from './pipes/included-in-map-style.pipe';
import { LevelTypeCirclePipe } from './pipes/level-type-circle.pipe';
import { MapTextCirclePipe } from './pipes/map-text-circle.pipe';
import { PreferencesComponent } from './components/routing/preferences/preferences.component';
import { ThousandNumberFormatPipe } from './pipes/thousand-number-format.pipe';
import { DetectDSAToggleButtonStylePipe } from './pipes/detect-dsatoggle-button-style.pipe';
import { Comma2dot } from './pipes/comma-convert-to-point.pipe';
import { ViewPortAspectRatioListComponent } from './components/view-port-aspect-ratio-list/view-port-aspect-ratio-list.component';
import { ModalComponent } from './components/modal/modal.component';
import { DrawBackgroundImagesService } from '../app/services/draw-background-images.service';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    FormsModule,
    SidebarModule,
    AppRoutingModule,
  ],
  declarations: [
    AppComponent,
    PopupComponent,
    MapEditorComponent,
    ManagerComponent,
    MapEditorStylePipe,
    MapsFromAreaPipe,
    LevelsMappedPipe,
    MapTextMappedPipe,
    IncludedInMapStylePipe,
    LevelTypeCirclePipe,
    MapTextCirclePipe,
    PreferencesComponent,
    ThousandNumberFormatPipe,
    DetectDSAToggleButtonStylePipe,
    Comma2dot,
    ViewPortAspectRatioListComponent,
    ModalComponent,
  ],
  providers: [
    DrawBackgroundImagesService
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
