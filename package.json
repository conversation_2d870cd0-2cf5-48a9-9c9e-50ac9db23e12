{"name": "mapsys", "author": "Dark Cloud", "version": "1.1.1", "main": "./main.js", "build": {"productName": "MapSys", "appId": "mapsys", "win": {"target": ["portable"]}, "portable": {"artifactName": "MapSys.exe"}, "directories": {"output": "electron/output", "app": "electron/app", "buildResources": "electron/buildResources"}}, "scripts": {"ng": "ng", "start": "ng serve --port 4210", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "electron": "electron .", "electron-build": "ng build --configuration production && electron .", "electron-package": "attrib -r /d /s && attrib -r /d /s C:/Users/<USER>/AppData/Local/Temp/electron-packager/win32-x64/dsadmin-win32-x64/resources/app && attrib -r /d /s C:/Users/<USER>/AppData/Local/Temp/electron-packager/win32-x64/mapsys-win32-x64/resources/app && electron-packager . --platform=win32 --overwrite"}, "private": true, "dependencies": {"@angular/animations": "~15.2.7", "@angular/common": "~15.2.7", "@angular/compiler": "~15.2.7", "@angular/core": "~15.2.7", "@angular/forms": "~15.2.7", "@angular/platform-browser": "~15.2.7", "@angular/platform-browser-dynamic": "~15.2.7", "@angular/router": "~15.2.7", "bootstrap": "^5.2.3", "bootstrap-notify": "^3.1.3", "chartist": "^1.3.0", "electron-context-menu": "^3.6.1", "jquery": "^3.6.4", "perfect-scrollbar": "1.5.5", "rxjs": "~7.8.0", "sweetalert2": "^11.7.3", "tslint": "^6.1.2", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "~15.2.6", "@angular/cli": "^15.2.6", "@angular/compiler-cli": "~15.2.7", "@types/jasmine": "~4.3.1", "@types/jasminewd2": "~2.0.10", "@types/node": "^18.15.11", "ajv": "^7.2.4", "codelyzer": "^6.0.2", "electron": "^24.1.1", "electron-packager": "^17.1.1", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.1", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "protractor": "~7.0.0", "ts-node": "~10.9.1", "tslib": "^2.5.0", "typescript": "~4.8.0"}}