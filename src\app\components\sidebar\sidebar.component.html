<div class="sidebar-wrapper" style="width: 100%">
  <div class="logo middle">
    <i
      style="cursor: pointer"
      (click)="PromptChangeLog()"
      class="pe-7s-compass"
    ></i>
    <h5>MapSys v{{ appVersion }}</h5>
  </div>
  <ul class="nav responsive-nav">
    <li>
      <app-dsa-file-menu></app-dsa-file-menu>
    </li>
      <li routerLinkActive="active">
      <a [routerLink]="[preferencesTab.path]">
        <i class="{{ preferencesTab.icon }}"></i>
        <p>{{ preferencesTab.title }}</p>
      </a>
    </li>
    <ng-container *ngIf="lastDSALoadedTime">
      <li routerLinkActive="active" *ngFor="let menuItem of menuTabs">
        <a [routerLink]="[menuItem.path]" >
          <i class="{{ menuItem.icon }}"></i>
          <p>{{ menuItem.title }}</p>
        </a>
      </li></ng-container
    >
    <li *ngIf="enabledMapEditor">
      <a class="sub-tab" [routerLink]="[mapEditorTab.path]">
        <i class="{{ mapEditorTab.icon }}"></i>
        <p>{{ mapEditorTab.title }}</p>
      </a>
    </li>
  </ul>
  <ul class="nav responsive-nav nav-bottom">
    <li>
      <app-mps-file-menu></app-mps-file-menu>
    </li>
  </ul>
  <!--  <ul class="nav responsive-nav">
    <li *ngIf="lastRemovals[0]?.obj">
      <a (click)="Undo()">
        <i class="pe-7s-prev"></i>
        <p [title]="lastRemovals[0]?.obj.id"> Undo removal </p>
      </a>
    </li>
  </ul> -->
</div>
<div class="sidebar-footer">build: {{ build }}</div>
