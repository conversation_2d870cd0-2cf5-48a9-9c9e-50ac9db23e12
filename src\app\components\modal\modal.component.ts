import { Component } from '@angular/core';
import { ModalService } from '../../../app/services/modal.service';

@Component({
  selector: 'app-modal',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss']
})

export class ModalComponent {

  constructor(public _modalService : ModalService)
  { }

  public closeModal()
  {
    this._modalService.canControlModal = !this._modalService.canControlModal;
  }

  /**
   * Calcula a classe CSS do modal baseada no número de itens
   *
   * Retorna diferentes classes para ajustar o tamanho do modal
   * dinamicamente baseado na quantidade de itens na tabela.
   *
   * @returns Classe CSS apropriada para o tamanho do modal
   */
  public getModalSizeClass(): string {
    const itemCount = this._modalService.itemEvent?.length || 0;

    if (itemCount === 1) {
      return 'modal-compact';
    } else if (itemCount <= 3) {
      return 'modal-small';
    } else if (itemCount <= 6) {
      return 'modal-medium';
    } else {
      return 'modal-large';
    }
  }

  /**
   * Calcula a classe CSS do container da tabela baseada no número de itens
   *
   * Este método determina qual classe aplicar ao container que envolve a tabela,
   * controlando a altura máxima e o comportamento do scroll.
   *
   * @returns Classe CSS apropriada para o container da tabela
   */
  public getTableContainerClass(): string {
    const itemCount = this._modalService.itemEvent?.length || 0;

    if (itemCount === 1) {
      return 'table-compact';
    } else if (itemCount <= 3) {
      return 'table-small';
    } else if (itemCount <= 6) {
      return 'table-medium';
    } else {
      return 'table-large';
    }
  }

  /**
   * Calcula a classe CSS da tabela baseada no número de itens
   *
   * @returns Classe CSS apropriada para o tamanho da tabela
   */
  public getTableSizeClass(): string {
    const itemCount = this._modalService.itemEvent?.length || 0;

    if (itemCount <= 3) {
      return 'table-compact';
    }

    return ''; // Usa estilos padrão para tabelas maiores
  }

  /**
   * Calcula a classe CSS das linhas baseada no número de itens
   *
   * Ajusta a altura das linhas da tabela para otimizar o espaço
   * baseado na quantidade total de itens.
   *
   * @returns Classe CSS apropriada para a altura das linhas
   */
  public getRowSizeClass(): string {
    const itemCount = this._modalService.itemEvent?.length || 0;

    if (itemCount === 1) {
      return 'row-compact';
    } else if (itemCount <= 3) {
      return 'row-small';
    } else {
      return 'row-normal';
    }
  }

  /**
   * Obtém o número de itens para debug/monitoramento
   *
   * @returns Número de itens na tabela
   */
  public getItemCount(): number {
    return this._modalService.itemEvent?.length || 0;
  }
}
