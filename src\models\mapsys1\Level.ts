import { ExportableClass } from './ExportableClass';
import { LevelType, prefixes } from './EnumMaps';
import { Area } from './Area';

/**
 * Game Level
 **/
export class Level extends ExportableClass 
{
  name: string;
  description: string;
  type: LevelType;
  speakerIds: string[] = [];
  battleCharacterIds: string[] = [];
  linkedLevelIds: string[] = [];
  dialogueIds: string[] = [];

  constructor(index: number, areaId: string) 
  {
    super(Level.generateId(areaId, index));
    this.type = LevelType.MINION;
  }

  static deepLoad(level: Level): Level 
  {
    return Object.assign(new Level(undefined, undefined), level);
  }
  public static generateId(areaId: string, index: number): string 
  {
    return areaId + '.' + prefixes.LEVEL + index;
  }
  public static getSubIdFrom(otherId: string): string 
  {
    return this.getSubId(this.generateId(Area.generateId(0), 0), otherId);
  }
}
