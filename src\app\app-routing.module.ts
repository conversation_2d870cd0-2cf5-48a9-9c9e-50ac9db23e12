import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ManagerComponent } from './components/routing/manager/manager.component';
import { MapEditorComponent } from './components/routing/map-editor/map-editor.component';
import { PreferencesComponent } from './components/routing/preferences/preferences.component';

export const routes: Routes = [
  { path: '', redirectTo: 'manager', pathMatch: 'full' },
  { path: 'preferences', component: PreferencesComponent },
  { path: 'manager', component: ManagerComponent },
  { path: 'map-editor', component: MapEditorComponent },
];

@NgModule({
  imports: [
    // CommonModule,
    // BrowserModule,
    RouterModule.forRoot(routes, {
      onSameUrlNavigation: 'reload',
      /*enableTracing: true,
      anchorScrolling: "enabled",*/
      scrollPositionRestoration: 'enabled',
    }),
  ],
  declarations: [],
  exports: [RouterModule],
})
export class AppRoutingModule {}

export const routingComponents = [
  ManagerComponent,
  MapEditorComponent,
];
