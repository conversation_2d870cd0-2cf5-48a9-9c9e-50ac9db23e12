
<!-- THIS CODE FOLLOWS THE FLEXBOX. THE EXCEPTION IS THE CANVAS
https://css-tricks.com/snippets/css/a-guide-to-flexbox/-->
<!-- START: Whole page div -->
<div style="display:flex; flex-direction: column; height: 100%; overflow-y: hidden;" (mousemove)="dragViewPort($event)" (mouseup)="releaseMouse()">

  <!-- START: Header div -->
  <div style="display:flex; flex-direction: row; justify-content: space-around; box-shadow: 0px 2px 8px 0px #888888;">
    <div class="title">
      {{ area?.name }}
    </div>
<!-- -------------------------------- -->
<ng-container *ngIf="isShowControlPoints">
    <div style="display:flex; flex-direction: column;" >
      <p style="align-self: center; padding-top: 10px; font-size: 20px;">Hermite Curve Properties</p>
      <div style="display:flex; flex-direction: row; justify-content: space-around; padding-bottom: 15px; height: 50px; align-items: end;">
        <div style="display:flex; flex-direction: column; align-items: center; margin-top: 10px; margin-right: 10px;">
          
    <!--  <label style="margin-bottom: -5px;">{{this.currentHermiteCurveThickness}}</label>-->
          
      <div class="thickness" title="Change the Hermite curve thickeness">
                <p style="margin-bottom: 0;">{{this.currentHermiteCurveThickness}}</p>
                <input 
                    type="range" 
                    min="1" max="10" 
                    #curveThickness 
                    (change)="controlLinesThickness(curveThickness.value)" 
                    [value]="this.currentHermiteCurveThickness">
            </div>
                    
          <!-- === RÉGUA SIMPLES COMO NO MODELO === -->
        <!--  <div class="simple-ruler-container">       
            <div class="ruler-base-line"></div>   
           <div class="main-tick" style="left: 0%;"></div>
            <div class="main-tick" style="left: 10%;"></div>
            <div class="main-tick" style="left: 20%;"></div>
            <div class="main-tick" style="left: 30%;"></div>
            <div class="main-tick" style="left: 40%;"></div>
            <div class="main-tick" style="left: 50%;"></div>
            <div class="main-tick" style="left: 60%;"></div>
            <div class="main-tick" style="left: 70%;"></div>
            <div class="main-tick" style="left: 80%;"></div>
            <div class="main-tick" style="left: 90%;"></div>
            <div class="main-tick" style="left: 100%;"></div>
       
           <div class="minor-tick" style="left: 1%;"></div>
            <div class="minor-tick" style="left: 2%;"></div>
            <div class="minor-tick" style="left: 3%;"></div>
            <div class="minor-tick" style="left: 4%;"></div>
            <div class="minor-tick" style="left: 5%;"></div>
            <div class="minor-tick" style="left: 6%;"></div>
            <div class="minor-tick" style="left: 7%;"></div>
            <div class="minor-tick" style="left: 8%;"></div>
            <div class="minor-tick" style="left: 9%;"></div>
            <div class="minor-tick" style="left: 11%;"></div>
            <div class="minor-tick" style="left: 12%;"></div>
            <div class="minor-tick" style="left: 13%;"></div>
            <div class="minor-tick" style="left: 14%;"></div>
            <div class="minor-tick" style="left: 15%;"></div>
            <div class="minor-tick" style="left: 16%;"></div>
            <div class="minor-tick" style="left: 17%;"></div>
            <div class="minor-tick" style="left: 18%;"></div>
            <div class="minor-tick" style="left: 19%;"></div>
            <div class="minor-tick" style="left: 21%;"></div>
            <div class="minor-tick" style="left: 22%;"></div>
            <div class="minor-tick" style="left: 23%;"></div>
            <div class="minor-tick" style="left: 24%;"></div>
            <div class="minor-tick" style="left: 25%;"></div>
            <div class="minor-tick" style="left: 26%;"></div>
            <div class="minor-tick" style="left: 27%;"></div>
            <div class="minor-tick" style="left: 28%;"></div>
            <div class="minor-tick" style="left: 29%;"></div>
            <div class="minor-tick" style="left: 31%;"></div>
            <div class="minor-tick" style="left: 32%;"></div>
            <div class="minor-tick" style="left: 33%;"></div>
            <div class="minor-tick" style="left: 34%;"></div>
            <div class="minor-tick" style="left: 35%;"></div>
            <div class="minor-tick" style="left: 36%;"></div>
            <div class="minor-tick" style="left: 37%;"></div>
            <div class="minor-tick" style="left: 38%;"></div>
            <div class="minor-tick" style="left: 39%;"></div>
            <div class="minor-tick" style="left: 41%;"></div>
            <div class="minor-tick" style="left: 42%;"></div>
            <div class="minor-tick" style="left: 43%;"></div>
            <div class="minor-tick" style="left: 44%;"></div>
            <div class="minor-tick" style="left: 45%;"></div>
            <div class="minor-tick" style="left: 46%;"></div>
            <div class="minor-tick" style="left: 47%;"></div>
            <div class="minor-tick" style="left: 48%;"></div>
            <div class="minor-tick" style="left: 49%;"></div>
            <div class="minor-tick" style="left: 51%;"></div>
            <div class="minor-tick" style="left: 52%;"></div>
            <div class="minor-tick" style="left: 53%;"></div>
            <div class="minor-tick" style="left: 54%;"></div>
            <div class="minor-tick" style="left: 55%;"></div>
            <div class="minor-tick" style="left: 56%;"></div>
            <div class="minor-tick" style="left: 57%;"></div>
            <div class="minor-tick" style="left: 58%;"></div>
            <div class="minor-tick" style="left: 59%;"></div>
            <div class="minor-tick" style="left: 61%;"></div>
            <div class="minor-tick" style="left: 62%;"></div>
            <div class="minor-tick" style="left: 63%;"></div>
            <div class="minor-tick" style="left: 64%;"></div>
            <div class="minor-tick" style="left: 65%;"></div>
            <div class="minor-tick" style="left: 66%;"></div>
            <div class="minor-tick" style="left: 67%;"></div>
            <div class="minor-tick" style="left: 68%;"></div>
            <div class="minor-tick" style="left: 69%;"></div>
            <div class="minor-tick" style="left: 71%;"></div>
            <div class="minor-tick" style="left: 72%;"></div>
            <div class="minor-tick" style="left: 73%;"></div>
            <div class="minor-tick" style="left: 74%;"></div>
            <div class="minor-tick" style="left: 75%;"></div>
            <div class="minor-tick" style="left: 76%;"></div>
            <div class="minor-tick" style="left: 77%;"></div>
            <div class="minor-tick" style="left: 78%;"></div>
            <div class="minor-tick" style="left: 79%;"></div>
            <div class="minor-tick" style="left: 81%;"></div>
            <div class="minor-tick" style="left: 82%;"></div>
            <div class="minor-tick" style="left: 83%;"></div>
            <div class="minor-tick" style="left: 84%;"></div>
            <div class="minor-tick" style="left: 85%;"></div>
            <div class="minor-tick" style="left: 86%;"></div>
            <div class="minor-tick" style="left: 87%;"></div>
            <div class="minor-tick" style="left: 88%;"></div>
            <div class="minor-tick" style="left: 89%;"></div>
            <div class="minor-tick" style="left: 91%;"></div>
            <div class="minor-tick" style="left: 92%;"></div>
            <div class="minor-tick" style="left: 93%;"></div>
            <div class="minor-tick" style="left: 94%;"></div>
            <div class="minor-tick" style="left: 95%;"></div>
            <div class="minor-tick" style="left: 96%;"></div>
            <div class="minor-tick" style="left: 97%;"></div>
            <div class="minor-tick" style="left: 98%;"></div>
            <div class="minor-tick" style="left: 99%;"></div>

     
         <input type="range" min="0" max="10" step="0.1" [value]="this.currentHermiteCurveThickness" #curveThickness (change)="controlLinesThickness(curveThickness.value)"
                class="simple-ruler-slider" id="myRange" title="Change the curve thickness">
        
          <div class="ruler-numbers" style="width: 100px;">
              <span class="ruler-number">0</span>
              <span class="ruler-number">1</span>
              <span class="ruler-number">2</span>
              <span class="ruler-number">3</span>
              <span class="ruler-number">4</span>
              <span class="ruler-number">5</span>
              <span class="ruler-number">6</span>
              <span class="ruler-number">7</span>
              <span class="ruler-number">8</span>
              <span class="ruler-number">9</span>
              <span class="ruler-number">10</span>
            </div>
        
          </div>
        -->
        </div>

        <div style="display:flex; flex-direction: column;  align-items: center;">
          <input
              type="color" id="colorpicker" [value]="this.currentHermiteCurveColor"
              #HermiteCurveColor (change)="controlHermiteColor(HermiteCurveColor.value)" title="Give a color to the curve">
        </div>

           <div class="thickness" title="Change the Hermite curve tension">
                <p style="margin-bottom: 0;">{{this.currentHermiteCurveTension}}</p>
                <input type="range" min="0" max="2" step="0.1" id="myRange"
                    [value]="this.currentHermiteCurveTension" 
                    #curveTension (change)="controlHermiteTension(curveTension.value)">
            </div>
      </div>
    </div>
</ng-container>
<!-- ----------------------------------------- -->
    <div id="menu" style="display: flex; flex-direction: row; gap: 10px; align-items: center; padding: 8px;">
      <!-- Grupo 1: Ações do Mapa -->
      <div style="display: flex; gap: 3px; align-items: center; border-right: 1px solid #dee2e6;">
        <button title="Download map" class="btn btn-success btn-fill effect-btn" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center;width: 60px;"
        (click)="downloadMap()">
        <i class="pe-7s-next-2" style="margin-left: 3px;"></i>
        </button>
        <button title="Print map" class="btn btn-success btn-fill effect-btn" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; width: 60px;"
            (click)="printMap()">
            <i class="pe-7s-print"></i>
        </button>
        <button title="Toggle level highlighting" class="btn btn-info btn-sm effect-btn" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; width: 60px;"
            [ngClass]="highlightLevelsInEditor ? 'btn-fill' : ''"
            (click)="toggleHighlightLevelsInEditor()">
            <i class="pe-7s-ribbon"></i>
        </button>
      </div>

      <!-- Grupo 2: Ferramentas de Edição -->
      <div style="display: flex; gap: 3px; align-items: center;">
        <button title="Draw on canvas" class="btn btn-success btn-fill blue-color" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; margin-right: 3px; width: 60px;"
            [ngStyle]="{'background-color': !isShowCanDraw ? '#C8C8C8' : ''}"
            (click)="this.canDraw()">
            <i class="pe-7s-pen"></i>
        </button>
        <button title="Enable/Disable control points" class="btn btn-success btn-fill blue-color" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; margin-right: 3px; width: 60px;"
            [ngStyle]="{'background-color': !isShowControlPoints ? '#C8C8C8' : ''}"
            (click)="this.disableControlPoints()">
            <i class="pe-7s-keypad"></i>
        </button>
        <button title="Show map in the background" class="btn btn-success btn-fill blue-color" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; margin-right: 3px; width: 60px;"
            [ngStyle]="{'background-color': !isShowBackground ? '#C8C8C8' : ''}"
            (click)="this.drawMapOnBackground()">
            <i class="pe-7s-map-2"></i>
        </button>
        <button title="Show items from the levels" class="btn btn-success btn-fill blue-color" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; margin-right: 3px; width: 60px;"
            [ngStyle]="{'background-color': !isShowItems ? '#C8C8C8' : ''}"
            (click)="this.showItems()">
            <i class="pe-7s-ticket"></i>
        </button>
      </div>

      <!-- Grupo 3: Visualização -->
      <div style="display: flex; gap: 3px; align-items: center; padding: 2px;">
        <button title="Show texts of places" class="btn btn-success btn-fill blue-color" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; margin-right: 3px; width: 60px;"
          [ngStyle]="{'background-color': !isShowText ? '#C8C8C8' : ''}"
          (click)="showText()">
          <i class="pe-7s-comment"></i>
        </button>
        <button title="Show images of places" class="btn btn-success btn-fill blue-color" style="padding: 6px 8px; margin: 0; min-width: 50px; height: 50px; display: flex; align-items: center; width: 60px;"
          [ngStyle]="{'background-color': !isShowImage ? '#C8C8C8' : ''}"
          (click)="this.showImage()">
          <i class="pe-7s-photo" style="margin-left: 3px;"></i>
        </button>
      </div>
    </div>
  </div>
  <!-- END: Header div -->

  <!-- START: Body div -->
  <div style="display:flex; flex-direction: row; justify-content: space-between; height: 100%; overflow-y: hidden;">

    <!-- START: left sidebar div -->
    <div class="div-sideBarModbile">
      <div style="display:flex; flex-direction: column; gap: 10px 0px; align-items: center; padding-top: 0;">
        <h2 style="font-size:2vh; align-self: center; margin-bottom: -5px;">Map</h2>
        <button style="width: 140px; justify-content: center; height: 32px;" type="number"
          class="btn btn-sm btn-fill btn-info effect-btn" (click)="promptChangeMapRatio()">
          {{ map?.aspectRatio.a }} x {{ map?.aspectRatio.c }}
        </button>
        <div style="text-align: center;">
          <span>Altura equivale a {{(map?.aspectRatio.a / viewPort?.ratio?.a) | number:'1.2-2'}} 
            {{(map?.aspectRatio.a / viewPort?.ratio?.a | number:'1.0-0') == 1 ? 'ecrã' : 'ecrãs'}}
          </span>

        </div>
        <div style="display:flex; flex-direction: row; gap: 10px 10px;">
          <button class="btn btn-sm btn-fill btn-primary btn-border-style effect-btn" title="Change the width of the main canvas"
            (click)="resizeMap('width')">
            <i class="pe-7s-up-arrow"></i>
          </button>
          <button class="btn btn-sm btn-fill btn-primary btn-border-style effect-btn" title="Change the height of the main canvas"
            (click)="resizeMap('height')">
            <i class="pe-7s-bottom-arrow"></i>
          </button>
        </div>
        <div style="display:flex; flex-direction: row; gap: 10px 10px;">
          <button style="width: 48px;"
            class="btn btn-sm btn-info btn-border-style effect-btn" title="Zoom in"
            (click)="startZooming(-1)"
            >
            <i class="pe-7s-search" style="display: flex; justify-content: center;"></i>
          </button>
          <button style="width: 48px;"
            class="btn btn-sm btn-info btn-fill btn-border-style effect-btn" title="Zoom out"
            (click)="startZooming(1)"
            >
            <i class="pe-7s-search" style="display: flex; justify-content: center;"></i>
          </button>
        </div>
        <div style="display:flex; flex-direction: row; gap: 10px 10px;">
            <p>{{ zoomValue | number: '2.0-2'}} %
        </div>
        <div *ngIf="this.isShowBackground">Image: {{this.ImagePercentage}} %</div>
        <div *ngIf="this.isShowBackground">Screen: {{this.ScreenPercentage | number:'1.2-2'}} %</div>
      </div>
<!-- ------------------------------------------ -->
      <div style="display:flex; flex-direction: column; gap: 10px 0px; align-items: center;" *ngIf="this.isShowCanDraw">
        <h2 style="font-size:2vh; align-self: center; margin-top: 0; margin-bottom: 3px;">Draw</h2>
        <button
            type="button" class="btn btn-danger effect-btn" style="padding:2px; width: 155px;"
            (click)="this.deleteDraw()" title="Delete ALL the draws on the canvas">Delete All
        </button>
        <button
            [ngStyle]="{'background-color': canDeleteDrawSection ? '#FF0059' : '', color:  canDeleteDrawSection ? '#FFFFFF' : '#FF0000'}"
            type="button" class="btn btn-danger effect-btn" style="padding:2px; width: 155px;" title="Delete a SELECTED segment on the canvas"
              (click)="this.deleteDrawSegment()">Delete Section
        </button>

        <div style="display:flex; flex-direction: row; justify-content: space-around; margin-bottom: 15px;">
          <input type="color" id="colorpicker" [value]="this.currentSelectedColor"  style="margin-top: 6.5px;" 
                #colorPicker (change)="getColorPicker(colorPicker.value)" title="Give a color to the curve">

            <div style="display: flex; flex-direction: column; align-items: center; width: 88px;">
                <label>{{currentCurveThickness}}</label>
                <input type="range" min="1" max="30" [value]="currentCurveThickness" #curveThickness (change)="getCurveThickness(curveThickness.value)"
                  style="width: 70px;" class="slider" id="myRange" title="Change the curve thickeness">
           </div>

        </div>
      </div>
<!-- -------------------------------------------- -->
      <div style="display:flex; flex-direction: column; gap: 10px 0px; align-items: center; margin-bottom: 70px;" >
        <h2 style="font-size:2vh; align-self: center; margin-top: 3px; margin-bottom: -5px;">Display</h2>
        <button style="width: 140px" class="btn btn-sm btn-fill btn-info effect-btn" title="Change the preview window viewport dimension" (click)="changeViewPortRatio()">
          {{ viewPort?.ratio?.c }} x {{ viewPort?.ratio?.a }}
        </button>
        <div class="phone-frame">
            <div class="phone-top">
              <div class="phone-notch"></div>
          </div>
         <canvas title="Map Preview"  width="100" height="100" id="view-port" (mousedown)="getViewPortPivot($event)"></canvas>
             <div class="phone-bottom">
            <div class="home-button"></div>
          </div>
        </div>
    </div>
    </div>
    <!-- END: left sidebar div -->

    <app-modal *ngIf="this._modalService.canControlModal"  (controlModal)="this.controlModalWindow($event)"></app-modal>

    <!-- START: Middle section div -->
    <div *ngIf="this.isShowBackground"  style="display:flex; flex-direction: column; justify-content: center; margin: 10px; padding:20px; background-color: #f5f5f5;">
      <div  style="display:flex; align-items: center; font-weight:800;">Canvas Dimensions</div>
      <br>
      <div>
        <div style="display:flex; flex-direction:column; width: 130px; justify-self: center; margin-bottom: 10px;">
          <label for="files">Measure Unit</label>
          <select #unit (change)="this.exchangeInch2CM(unit.value)">
            <option value="Inches">Inches</option>
            <option value="Centimeters">Centimeters</option>
          </select>
        </div>
        <div style="display:flex; flex-direction:column; margin-bottom: 10px; align-items: center;">
          <label for="files">Diagonal Size:</label>
          <input #diagonal (keyup)="currentDimensions(diagonal.value)" placeholder="Enter diagonal" title="Enter diagonal" type="number">
        </div>
        <div style="display:flex; flex-direction:column; margin-bottom: 10px; align-items: center;">
          <label for="files">Desired PPI:</label>
          <input #ppi [value]="this.desiredPPI" (keyup)="desiredDimensions(ppi.value)" placeholder="Enter Desired PPI" title="Enter Desired PPI" type="number">
        </div>
      </div>
      <button class="btn-Listdevice effect-btn" (click)="this.calculateResolution()" style="margin-top: 5px;">Calculate Resolution</button>

    <!-- ----------------------------------------------------- -->
        <button class="btn-Listdevice effect-btn" (click)="this.showDevicesList()" style="margin-top: 10px; width: 130px; align-self: center;">List of Devices</button>
        <div style="overflow-y: scroll; margin-top: 5px;" *ngIf="this.canShowDevicesList">
          <ng-container *ngFor="let device of this.devices">
              <div (click)="this.showSelectedDevice(device)" class="card" style="padding:8px; background-color:#ffffff; cursor: pointer;">
                  <p class="card-text">Device Name: {{ device.deviceName }}</p>
                  <p class="card-text">Device Type: {{ device.deviceType }}</p>
                  <p class="card-text">Device Size: {{ device.deviceSize }}</p>
                  <p class="card-text">Device PPI: {{ device.devicePPI }}</p>
                  <p class="card-text">Device Resolution: {{ device.deviceResolution }}</p>
              </div>
          </ng-container>
        </div>
        <div (click)="this.closeSelectedDevice()" *ngIf="this.canShowSelectedDevice" class="card" style="padding:8px; background-color:#ffffff;">
          <p class="card-text">Device Name: {{ this.selectedDevice.deviceName }}</p>
          <p class="card-text">Device Type: {{ this.selectedDevice.deviceType }}</p>
          <p class="card-text">Device Size: {{ this.selectedDevice.deviceSize }}</p>
          <p class="card-text">Device PPI: {{ this.selectedDevice.devicePPI }}</p>
          <p class="card-text">Device Resolution: {{ this.selectedDevice.deviceResolution }}</p>
      </div>
      </div>
    <!-- ------------------------------------------------ -->

    <div id="editor-wrapper" [ngStyle]="this.map?.aspectRatio?.c > this.map?.aspectRatio?.a"
      class="middle-window">

      <!-- Container centralizado para o mapa -->
      <div class="map-container">

        <!-- Wrapper para canvas e div de interação -->
        <div class="canvas-wrapper" style="position: relative; display: inline-block;">

          <!-- Canvas único para todas as funcionalidades -->
          <canvas id="unified-canvas" class="unified-canvas-map"></canvas>

          <!-- Div de interação sobreposta para capturar eventos do mouse -->
          <div id="interaction-layer" class="div-interaction-layer"
               (mousemove)="handleMouseMove($event)"
               (mousedown)="handleMouseDown($event)"
               (mouseup)="handleMouseUp($event)"
               (mouseleave)="handleMouseLeave($event)">
          </div>

        </div>
      </div>
    </div>
    <!-- END: Middle section div -->

      <!-- START: right sidebar div -->
    <div style="display:flex; flex-direction: column; height: 100%; overflow-y: auto; box-shadow: -2px 8px 8px 0px #888888; padding: 20px;">

      <ng-container *ngFor="let level of levelsFromArea; let i = index">
        <div [id]="level.id" class="tr-clickable level-row" [style.min-width]="levelHeights[level.id] * 30 + 'px'" (click)="createPoint(level.id)">
          <div class="circle-container">
            <div class="circle branch-circle middle alignLevels-circle"
              [ngStyle]="level.type | levelTypeCircle : level.id : map.points : newLevelIds">
              {{ _areaService.getLevelIndex(level.id) }}
            </div>
          </div>
          <div class="level-name-text"
            [ngClass]="level.id | includedInMapStyle: map.points : newLevelIds">
            {{level.name}}
          </div>
        </div>
      </ng-container>
      <!-- ------------------------ -->
      <ng-container *ngFor="let level of textPoints; let i = index">
        <div [id]="level.id" class="tr-clickable color-line"
             style="display:flex; flex-direction: row; gap: 0px 20px;border-bottom: 1px solid rgb(192, 192, 192); width: 300px;  margin-bottom: 3px;  margin-top: 3px;"
             (mouseenter)="highlightTextPointOnMap(level.id, true)"
             (mouseleave)="highlightTextPointOnMap(level.id, false)"
             (click)="navigateToTextPoint(level.id)">
          <div class="circle branch-circle middle align-circle"
            [ngStyle]="level.classification | mapTextCircle">{{level.id}}
          </div>
          <div style="display: contents; width: -moz-fit-content; padding-right: 5px;"
            [ngClass]="level.id | includedInMapStyle: map.points : newLevelIds">
            {{level.name}}
          </div>
        </div>
      </ng-container>
    </div>
    <!-- END: right sidebar div -->

  </div>
  <!-- END: Body div -->

</div>
<!-- END: Whole page div -->
