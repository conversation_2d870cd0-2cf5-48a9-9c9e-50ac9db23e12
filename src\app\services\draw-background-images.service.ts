import { Injectable } from '@angular/core';
import { Canvas } from '../components/routing/map-editor/Canvas';
import { Area } from 'src/models/mapsys1';


@Injectable({
  providedIn: 'root',
})
export class DrawBackgroundImagesService
{
    public image = new Image();

    public drawImageOnBackground(area : Area, editorPort : Canvas)
    {
        this.image.src = './assets/mapBackgrounds/'+area.hierarchyCode+'.png'
        editorPort.context.drawImage(this.image, 0, 0,  editorPort.canvasDimension.width, editorPort.canvasDimension.height);
    }
}