import { Injectable } from '@angular/core';
import { AppService } from './app.service';
import { AreaService } from './area.service';
import { LevelService } from './level.service';
import { MapService } from './map.service';
import { MapPointsService } from './map-points.service';
import { UserSettingsService } from './user-settings.service';
import { File } from 'src/custom/File';
import { ItemService } from './item.service';
import { EventService } from './event.service';

@Injectable({
  providedIn: 'root'
})
export class DataService 
{

  constructor(
    private _areaService : AreaService,
    private _levelService: LevelService,
    private _mapService: MapService,
    public _mapPointsService: MapPointsService,
    private _appService: AppService,
    private _userSettingsService: UserSettingsService,
    private _itemService : ItemService,
    private _eventService : EventService,
  ) { }
  
  importMps(mps: File.MPS1)
  {
    const loadTime = new Date();

    this._mapService.importPackage(mps.mapPackage);
    this._mapPointsService.importPackage(mps.mapPointsPackage);
    this._userSettingsService.importData(mps.userSettings);
    this._appService.setMPSLastLoadedTime(loadTime);
  }
  
  exportMps()
  {
    const dateTime = new Date();
    const exportedDateTime = dateTime.toLocaleDateString();

    const file = new File.MPS1(null, exportedDateTime, this._appService, this._mapService, 
      this._mapPointsService, this._userSettingsService.data);

    return file;
  }


  importDsa(dsa: File.DSA)
  {
    const loadTime = new Date();
    this._areaService.importPackage(dsa.myAreaPackage);
    this._levelService.importPackage(dsa.myLevelPackage);
    this._mapPointsService.importPackage(dsa.myMapPointsPackage);
    this._itemService.importPackage(dsa.myItemPackage);
    this._eventService.importPackage(dsa.myEventPackage);
    this._appService.setDSALastLoadedTime(loadTime);
  }

  clearData()
  {
    this._areaService.reset();
    this._levelService.reset();
    this._itemService.reset();
    this._eventService.reset();
    this._mapService.reset();
    this._mapPointsService.reset();
    this._userSettingsService.reset();
    this._appService.setMPSLastLoadedTime(undefined);
    this._appService.setDSALastLoadedTime(undefined);
  }

  getData()
  {
    return {
      areaData: this._areaService.data,
      levelData: this._levelService.data,
      itemData: this._itemService.data,
      eventData: this._eventService.data,
      mapData: this._mapService.data,
      mapPointsData: this._mapPointsService.data,
      userSettings: this._userSettingsService.data
    };
  }

}
