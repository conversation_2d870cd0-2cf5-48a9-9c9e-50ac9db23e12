import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'mapTextCircle',
  pure: false,
})
export class MapTextCirclePipe implements PipeTransform {

  transform(classification): unknown {
    return {
      'background-color': classification === 'text' ? 'red' : 'blue',
      color: 'white',
      cursor: 'pointer',
      'margin-right': '5px',
      'line-height': '25px',
      border:  '1px solid black'
    };
  }
}
