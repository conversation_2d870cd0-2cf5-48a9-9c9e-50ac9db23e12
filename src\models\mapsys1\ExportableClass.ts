import { prefixes } from './EnumMaps';

export abstract class ExportableClass 
{
  public constructor(public id: string) { }

  public static getIndex<T extends string | number>(prefix: prefixes, objectId: string) : T 
  {
    return parseInt(objectId?.split('.').find(part => part.includes(prefix))?.replace(prefix, ''), 10) as unknown as T;
  }
  // A2       L20          D1_PT-BR        SB0
  // A       L          D        SB
  protected static getSubId(exampleSubId: string, objectId: string): string 
  {
    const splittedId = objectId?.split('.');
    const subId: string[] = [];
    exampleSubId.split('.').forEach(exampleSubIdSection => 
    {
      const numberIndex = exampleSubIdSection.indexOf('0');
      const sectionPrefix = exampleSubIdSection.slice(0, numberIndex);
      subId.push(splittedId?.find(idSection => idSection.includes(sectionPrefix)));
    })
    return subId.join('.');
  }

}
