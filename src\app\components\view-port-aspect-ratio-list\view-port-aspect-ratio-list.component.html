
    <div class="card-header">
      <div class="row">
        <div class="col-auto">
          <h3>Aspect Ratios</h3>
        </div>
        <div class="col-auto">
          <button
            class="btn btn-simple btn-success"
            (click)="promptAddAspectRatio()">
            <i class="pe-7s-plus"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="container-fluid">
      <div class="col-md-12">
        <table class="table table-list">
          <thead>
            <tr>
              <th >Aspect Ratio (W)</th>
              <th > x </th>
              <th >Aspect Ratio (H)</th>
              <th >Pixels (W)</th>
              <th >Pixels (H)</th>
              <th ></th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let aspectRatio of aspectRatios">
              <tr class="table-body" (click)="this.updateMapAspectRatio(aspectRatio.c, aspectRatio.a)">
                <td>
                    {{ aspectRatio.c }} 
                </td>
                <td>
                     x 
                </td>
                <td>
                    {{ aspectRatio.a }}
                </td>
                <td *ngIf="aspectRatio.c < aspectRatio.a">
                    {{ 1920 * (aspectRatio.c/aspectRatio.a) | number : '1.0' }}
                </td>
                <td *ngIf="aspectRatio.c < aspectRatio.a">
                    {{ 1920 }}
                </td>

                <td *ngIf="aspectRatio.c > aspectRatio.a">
                  {{ 1920 }}
                </td>
                <td *ngIf="aspectRatio.c > aspectRatio.a">
                    {{ 1920 * (aspectRatio.a/aspectRatio.c) | number : '1.0' }}
                </td>

                <td class="td-auto">
                  <button class="btn btn-sm btn-simple btn-danger" (click)="removeAspectRatio(aspectRatio)">
                    <i class="pe-7s-less"></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
