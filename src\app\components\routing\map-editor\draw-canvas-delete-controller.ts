import { SaveDrawsController } from './save-draws-controller';

export class DrawCanvasDeleteController {
  constructor(
    private canDraw: boolean,
    private canDelete: boolean,
    private ctx: CanvasRenderingContext2D,
    private saveDrawsController: SaveDrawsController,
    private mapName: string,
    private canVerifyAgain: boolean
  ) {}

  /**
   * Método chamado no construtor para remover "números-lixo" (pode ser redefinido conforme necessário).
   */
  deleteTrashNumbers(): void {
    // Exemplo: Limpar pontos inválidos salvos (mock)
    const data = this.saveDrawsController.loadPointsFromLocalStorage(this.mapName)!;
    if (!data || !data.drawPoints) return;

    const filtered = data.drawPoints.filter(p => p.x !== null && p.y !== null && p.x >= 0 && p.y >= 0);
    data.drawPoints = filtered;
    this.saveDrawsController.savePointsOnLocalStorage(this.mapName, data);
  }

  /**
   * Deleta um segmento do desenho com base em um índice encontrado (ex: ao clicar em um ponto).
   * @param drawData Dados salvos com pontos do desenho
   * @param index Índice do ponto a ser deletado
   * @returns true se permitida nova verificação, false caso contrário
   */
  deleteDrawSegment(drawData: any, index: number): boolean {
    if (!drawData || !drawData.drawPoints || index < 0 || index >= drawData.drawPoints.length) {
      return true;
    }

    // Remove os dois pontos (anterior e atual) como exemplo de segmento
    const segmentSize = 2;
    drawData.drawPoints.splice(index, segmentSize);

    this.saveDrawsController.savePointsOnLocalStorage(this.mapName, drawData);

    // Limpa e redesenha
    this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
    this.redraw(drawData);
    return true;
  }

  /**
   * Redesenha os pontos após exclusão
   */
  private redraw(drawData: any): void {
    const points = drawData.drawPoints;
    for (let i = 0; i < points.length - 1; i++) {
      const p1 = points[i];
      const p2 = points[i + 1];

      if (p1.x === -1 || p2.x === -1) continue;

      this.ctx.beginPath();
      this.ctx.strokeStyle = p1.color || '#000';
      this.ctx.lineWidth = p1.thickness || 5;
      this.ctx.lineCap = 'round';
      this.ctx.moveTo(p1.x, p1.y);
      this.ctx.lineTo(p2.x, p2.y);
      this.ctx.stroke();
    }
  }
}
