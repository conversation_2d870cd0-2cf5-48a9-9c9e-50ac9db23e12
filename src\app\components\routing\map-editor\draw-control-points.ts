import { Canvas } from './Canvas';
import { AnchorPoint, Map } from '../../../../models/mapsys1';

export class DrawControlPoints 
{
     public ctx!: CanvasRenderingContext2D;

    constructor(private levelsFromArea, private editorPort : Canvas) 
    {
      this.editorPort = editorPort;
      this.ctx = this.editorPort.context;
      this.levelsFromArea = levelsFromArea;
    }

    public drawControlpointNLinksBetweenPointNControlpoint(selectedLevel, mapPoints)
    {
        for(let i = 0; i < mapPoints.points[selectedLevel].controlPosition.length; i++)
        {
            this.drawControlPoint(mapPoints.points[selectedLevel].controlPosition[i]);
    
            this.drawLinkBetweenPointNControlpoint
            (
                mapPoints.points[selectedLevel].position, 
                mapPoints.points[selectedLevel].controlPosition[i]
            );
        }
    }
  
    private drawControlPoint(controlPosition: AnchorPoint,radius: number = 5, color: string = 'blue')
    {
      this.ctx.beginPath();
      this.ctx.fillStyle = color;
      this.ctx.arc
      (
        this.editorPort.positionInPixelsForAnchorpoint(controlPosition).xd,
        this.editorPort.positionInPixelsForAnchorpoint(controlPosition).yd,
        radius, 0, 2 * Math.PI
      );
      this.ctx.fill();
    }
  
    private drawLinkBetweenPointNControlpoint(point: AnchorPoint, controlPoint: AnchorPoint, color: string = 'orange')
    {
      this.ctx.beginPath();
      this.ctx.strokeStyle = color;
      this.ctx.moveTo
      ( 
        this.editorPort.positionInPixelsDeadzone(point).xd,
        this.editorPort.positionInPixelsDeadzone(point).yd
      );

      this.ctx.lineTo
      (
        this.editorPort.positionInPixelsForAnchorpoint(controlPoint).xd,
        this.editorPort.positionInPixelsForAnchorpoint(controlPoint).yd
      );

      this.ctx.stroke();
    }
}