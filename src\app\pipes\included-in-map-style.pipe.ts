import { Pipe, PipeTransform } from '@angular/core';
import { LevelPoint } from 'src/models/mapsys1';

@Pipe({
  name: 'includedInMapStyle',
  pure: false,
})
export class IncludedInMapStylePipe implements PipeTransform {
  transform(
    levelId: string,
    points: LevelPoint[],
    newLevelIds: string[]
  ): string {
    if (points[levelId]) {
      if (newLevelIds?.includes(levelId)) {
        return 'btn btn-warning';
      }
      return 'btn';
    }
    return 'btn btn-error';
  }
}
