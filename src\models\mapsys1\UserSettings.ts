import { Ratio } from '../mapsys1';

export interface UserSettings {
  levelDiameter: number;
  aspectRatios: Ratio[];
  detectDSADataFromLocalStorage: boolean;
  deadZoneSize: number;
  hermiteCurveThickness: number;
  hermiteCurveColor: string;
  hermiteCurveTension: number;
  currentSelectedColor: string;
  curveThickness: number;
  highlightLevelsInEditor: boolean,
  editorConfigurations: {
    [mapId: string]: {
      viewPortPivot: { x: number, y: number };
      zoomValue: number;
    };
  };
}
