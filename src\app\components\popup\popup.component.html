<div class="swal2-container swal2-center swal2-backdrop-show">
  <div style="display: contents; flex-direction: row;">
    <ng-container
      *ngFor="let popupInterface of popupInterfaces; let interfaceIndex = index"
    >
      <div
        id="popup"
        class="window swal2-modal swal2-show"
        aria-labelledby="swal2-title"
        aria-describedby="swal2-content"
        tabindex="-1"
        role="dialog"
        aria-live="assertive"
        aria-modal="true"
        style="display: flex; margin: 10px;"
      >
        <div class="swal2-header">
          <h2 class="swal2-title">{{ popupInterface.style.title }}</h2>
        </div>
        <div class="swal2-content">
          <div class="swal2-html-container" style="display: block;">
            {{ popupInterface.style.text }}
          </div>
        </div>
        <div
          [ngClass]="'actions-' + popupInterface.style.actionsClass"
          style="
            overflow: auto;
            margin: 10px;
            max-height: 80vh;
            align-self: center;
          "
        >
          <ng-container
            *ngFor="
              let button of popupInterface.buttons;
              let buttonIndex = index
            "
          >
            <button ngClass
              ngClass="btn {{
                'btn-' +
                  popupInterface.style.buttonSize +
                  ' ' +
                  button.styling.klass
              }}
            {{
                button === selectedButton[interfaceIndex]
                  ? 'btn-fill btn-primary'
                  : ''
              }}"
              (click)="ClickButton(interfaceIndex, buttonIndex)"
            >
              {{ button.text }}
              <br/>
              <div
                *ngIf="button.styling.divStyle"
                [style]="button.styling.divStyle"
              >
                {{ button.styling?.divText }}
              </div>
            </button>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>
</div>
