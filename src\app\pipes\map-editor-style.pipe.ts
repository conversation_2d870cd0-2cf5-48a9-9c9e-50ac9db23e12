import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'mapEditorStyle',
  pure: false,
})
export class MapEditorStylePipe implements PipeTransform {
  transform(isHorizontal: boolean): unknown {
    if (!isHorizontal) {
      return {
        'overflow-x': 'scroll',
        display: 'grid',
        'align-items': 'center',
      };
    }
    return {
      'overflow-x': 'scroll',
      display: 'grid',
      'align-items': 'center',
    };
  }
}
