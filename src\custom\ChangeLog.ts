import { SweetAlertOptions } from 'sweetalert2';

export const ChangeLog: SweetAlertOptions[] = [
  {
    title: '2020/08/30 (MAPSYS v1.0.2): Patches',
    html: `<div style="text-align: left">

      <h5>Level List</h5>
      <ul>
      <li>Fixed icon button
      </ul>`,
  },
  {
    title: '2021/08/21 (MAPSYS v1.1.0): Changes',
    html: `<div style="text-align: left">

      <h5>Data Utility</h5>
      <ul>
      <li>Now compatible with DSA9 files
      <li>Backwards compatible with previous formats
      </ul>
      <h5>Map Generation</h5>
      <ul>
      <li>New pattern for newly instantiated maps
      <li>Newly instanced levels are now placed near their parent level
      <li>Map points are now clamped within acceptable data boundaries
      </ul>
      <h5>General</h5>
      <ul>
      <li>Improved system performance
      </ul>`,
  }
];

