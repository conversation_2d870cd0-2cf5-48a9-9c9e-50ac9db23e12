import { Area } from '../../../../models/mapsys1';
import { Canvas } from './Canvas';

export class EditorUtilities
{
  levelsFromArea
  map
  userSettingsService
  detectedLevelId
  viewPort
  newLevelIds
  offsetBoundaries
  textPoints 
  firsTimeout;

  constructor(
    private mapCanvas: Canvas, 
    private drawCanvas: Canvas, 
    private itemCanvas: Canvas, 
    private editorPort : Canvas, 
    private area: Area, 
    private version: string
    ) { }

  initializer(levelsFromArea, map, userSettingsService, detectedLevelId, viewPort, newLevelIds, offsetBoundaries, textPoints)
  {
    this.levelsFromArea = levelsFromArea
    this.map = map
    this.userSettingsService = userSettingsService
    this.detectedLevelId = detectedLevelId
    this.viewPort = viewPort
    this.newLevelIds = newLevelIds
    this.offsetBoundaries = offsetBoundaries
    this.textPoints = textPoints
  }

  downloadMap(): void 
  {
    this.drawCanvasSequence();
    this.footerDateNameVersion();
    
    //transform the canvas content into png
    const mapImage = this.mapCanvas.el.toDataURL('image/png');
    const dateTime = new Date();
    const exportedDateTime = dateTime.toLocaleDateString() + '_' + dateTime.toLocaleTimeString();
    const anchorElement = document.createElement('a');
    anchorElement.href = mapImage;
    anchorElement.download = exportedDateTime + '.png';
    anchorElement.click();
  }

  printMap(): void 
  {
    this.drawCanvasSequence();
    this.footerDateNameVersion();
    //transform the canvas content into png
    const mapImage = this.mapCanvas.el.toDataURL('image/png');
    
    //Open a new window with the mapImage content.
    const mapWindow: Window = window.open(mapImage);
    
    //Image will be the HTML element that will receive the content of the mapImage. width and hieght are from 4A pixels size.
    let markup = '<img src="' + mapImage + '"width="' +  2480*0.3  + /*'"' + 'height="' + 3508*0.3 + */'" />'
    
    //Write the mapImage content in the mapWindow.
    mapWindow.document.write(markup);
    this.firsTimeout = setTimeout(() => 
    {
      mapWindow.print();
      mapWindow.close();
    }, 100);
  }

  footerDateNameVersion()
  {
    this.mapCanvas.context.beginPath();
    //Draw map name
    this.mapCanvas.context.fillStyle = "#a6a6a6";
      this.mapCanvas.context.font = '20px Arial';
      this.mapCanvas.context.fillText('Map Name: ' + this.area.name,
      this.mapCanvas.canvasDimension.width * 0.25, 
      this.mapCanvas.canvasDimension.height - this.mapCanvas.deadZoneInPixels * 0.7,
      this.mapCanvas.canvasDimension.width);

      //Draw data, time and version
      this.mapCanvas.context.font = '20px Arial';
      const now = new Date();
      this.mapCanvas.context.fillText(now.toLocaleDateString() + ' ' + now.toLocaleTimeString() + ' (MapSys v' + this.version + ')',
      this.mapCanvas.canvasDimension.width * 0.25, 
      this.mapCanvas.canvasDimension.height - this.mapCanvas.deadZoneInPixels * 0.3,
      this.mapCanvas.canvasDimension.width);
    this.mapCanvas.context.closePath();
  }


  drawCanvasSequence()
  {
    //The order is important! For more info look for the method name in the documentation.
    let canvasList : Canvas[] = [ this.editorPort, this.itemCanvas, this.drawCanvas];

    for(let i = 0; i < canvasList.length; i++)
    {
      this.mapCanvas.context.drawImage(canvasList[i].el, 0, 0, this.mapCanvas.el.width, this.mapCanvas.el.height);
    }
  }

}