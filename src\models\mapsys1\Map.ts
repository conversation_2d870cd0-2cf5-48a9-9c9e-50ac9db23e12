import { Ratio } from './ScreenProperties';
import { LevelPoint } from './LevelPoint';
import { ExportableClass } from './ExportableClass';
import { prefixes } from './EnumMaps';

export class Map extends ExportableClass 
{
  constructor(public areaId: string, index: number, public aspectRatio: Ratio) 
  {
    super(Map.generateId(index));
  }

  points: 
  {
    [levelId: string]: LevelPoint;
  } = {};

  static deepLoad(map: Map): Map 
  {
    const deepLoadedMap = Object.assign(new Map(undefined, undefined, undefined), map);
    deepLoadedMap.aspectRatio = Ratio.deepLoad(map.aspectRatio);
    return deepLoadedMap;
  }

  public static generateId(index: number): string 
  {
    return prefixes.MAP + index;
  }
}
