import { Canvas } from './Canvas';
interface Position
{
    x: number;
    y: number;
}

interface DrawStroke
{
    fromX: number;
    fromY: number;
    toX: number;
    toY: number;
    color: string;
    thickness: number;
}

export class DrawOnCanvas
{
    public context!: CanvasRenderingContext2D;
    image = new Image;
    oldMousePosition : Position = {x:0, y:0};
    currentCurveColor : string = 'blue';
    currentCurveThicknees : number = 1;

    // Array para armazenar os traços de desenho
    private drawStrokes: DrawStroke[] = [];

    constructor(private areaName: string, private drawCanvas: Canvas)
    {
        this.context = this.drawCanvas.context;
        this.loadDrawStrokesFromLocalStorage();
    }

    /**
     * Carrega os traços de desenho salvos do localStorage
     */
    private loadDrawStrokesFromLocalStorage(): void
    {
        const savedStrokes = localStorage.getItem(this.areaName + '_strokes');
        if (savedStrokes) {
            try {
                this.drawStrokes = JSON.parse(savedStrokes);
            } catch (e) {
                this.drawStrokes = [];
            }
        }
    }

    /**
     * Salva os traços de desenho no localStorage
     */
    private saveDrawStrokesToLocalStorage(): void
    {
        localStorage.setItem(this.areaName + '_strokes', JSON.stringify(this.drawStrokes));
    }

    /**
     * Redesenha todos os traços salvos no canvas
     */
    private redrawAllStrokes(): void
    {
        this.drawStrokes.forEach(stroke => {
            this.context.beginPath();
            this.context.lineCap = 'round';
            this.context.lineWidth = stroke.thickness;
            this.context.strokeStyle = stroke.color;
            this.context.moveTo(stroke.fromX, stroke.fromY);
            this.context.lineTo(stroke.toX, stroke.toY);
            this.context.stroke();
        });
    }

    /**
     * Método público para compatibilidade - não faz nada agora
     */
    public updateDrawOnlyCanvasDimensions(): void
    {
        // Método mantido para compatibilidade, mas não faz nada
        // pois agora usamos array de traços em vez de canvas temporário
    }

    /**
     * Desenha uma linha no canvas principal e salva o traço no array
     * O canvas principal mostra o desenho ao usuário
     * O array de traços é usado para salvar apenas os desenhos no localStorage
     */
    drawOnMouseDown(mousePosition: any): void
    {
        if(this.oldMousePosition.x != mousePosition.x &&
            this.oldMousePosition.y != mousePosition.y &&
            mousePosition.x != 0 && mousePosition.y != 0 &&
            this.oldMousePosition.x != 0 && this.oldMousePosition.y != 0)
        {
            // Desenha no canvas principal (visível ao usuário)
            this.context.beginPath();
            this.context.lineCap = 'round';
            this.context.lineWidth = this.currentCurveThicknees;
            this.context.strokeStyle = this.currentCurveColor;
            this.context.moveTo(this.oldMousePosition.x, this.oldMousePosition.y);
            this.context.lineTo(mousePosition.x, mousePosition.y);
            this.context.stroke();

            // Salva o traço no array para persistência
            const stroke: DrawStroke = {
                fromX: this.oldMousePosition.x,
                fromY: this.oldMousePosition.y,
                toX: mousePosition.x,
                toY: mousePosition.y,
                color: this.currentCurveColor,
                thickness: this.currentCurveThicknees
            };
            this.drawStrokes.push(stroke);

            this.oldMousePosition.x = mousePosition.x;
            this.oldMousePosition.y = mousePosition.y;
        }
    }

    /**
     * Salva apenas os desenhos do usuário no localStorage
     * Usa o array de traços que contém apenas os desenhos, sem círculos, linhas, etc.
     */
    saveDrawOnLocalStorage(): void
    {
        // Salva o array de traços no localStorage
        this.saveDrawStrokesToLocalStorage();
    }

    /**
     * Carrega os desenhos salvos do localStorage e os exibe no canvas principal
     * Redesenha todos os traços salvos
     */
    getDrawFromLocalStorage(drawCanvas?: any): void
    {
        // Carrega os traços do localStorage
        this.loadDrawStrokesFromLocalStorage();

        // Redesenha todos os traços no canvas
        this.redrawAllStrokes();
    }

    /**
     * Funciona como um "apagador" que remove apenas os desenhos do usuário
     * Remove traços que intersectam com a área do mouse, sem afetar outros elementos do mapa
     * Retorna true se algum traço foi removido, false caso contrário
     */
    clearCanvasMousePosition(mousePosition: any): boolean
    {
        const clearRadius = 25;
        const initialStrokeCount = this.drawStrokes.length;

        // Remove traços que intersectam com a área do mouse
        this.drawStrokes = this.drawStrokes.filter(stroke => {
            // Verifica se o traço intersecta com a área de limpeza
            const intersects = this.lineIntersectsCircle(
                stroke.fromX, stroke.fromY, stroke.toX, stroke.toY,
                mousePosition.x, mousePosition.y, clearRadius
            );
            return !intersects;
        });

        const strokesWereRemoved = this.drawStrokes.length !== initialStrokeCount;

        // Se algum traço foi removido, salva as mudanças
        if (strokesWereRemoved) {
            this.saveDrawOnLocalStorage();
        }

        // IMPORTANTE: Não limpa o canvas principal diretamente para não apagar
        // círculos, linhas de limite, etc. O redesenho completo será feito
        // pelo componente principal através do updateMapCanvas()

        return strokesWereRemoved;
    }

    /**
     * Verifica se uma linha intersecta com um círculo
     */
    private lineIntersectsCircle(x1: number, y1: number, x2: number, y2: number, cx: number, cy: number, radius: number): boolean
    {
        // Distância do ponto ao segmento de linha
        const A = cx - x1;
        const B = cy - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;
        if (lenSq !== 0) {
            param = dot / lenSq;
        }

        let xx: number, yy: number;
        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = cx - xx;
        const dy = cy - yy;
        return dx * dx + dy * dy <= radius * radius;
    }

    /**
     * Limpa completamente todos os desenhos
     * Remove todos os traços salvos
     */
    clearAllCanvas(): void
    {
        // Limpa o array de traços
        this.drawStrokes = [];

        // Limpa o canvas principal
        this.context.clearRect(0, 0, this.drawCanvas.canvasDimension.width, this.drawCanvas.canvasDimension.height);

        // Salva o estado limpo
        this.saveDrawOnLocalStorage();
    }

    setCurveColor(color: string)
    {
        this.currentCurveColor = color;
    }

    getCurveColor() : string
    {
       return this.currentCurveColor;
    }

    setCurveThickness(thickness: number)
    {
        this.currentCurveThicknees = thickness;
    }

    getCurveThickness() : number
    {
        return this.currentCurveThicknees;
    }
}