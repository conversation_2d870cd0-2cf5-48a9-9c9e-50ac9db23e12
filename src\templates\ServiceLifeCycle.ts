/**
 * <AUTHOR>
 * @fileoverview This file contains life abstract classes that implements life hook cycles.
 */

import { Alert } from '../custom/Alert';
import { ExportableClass } from '../models/mapsys1';

/**
 * A namespace for life hook cycle classes to be extended from.
 */
export abstract class ServiceLifeCycle<T extends ExportableClass> {
  constructor(public readonly typeName: string) {}

  /**
   * Triggers srvLoad and then srvAfterLoad in order
   */
  public load(): void {
    this.srvLoad();
    this.srvAfterLoad();
  }

  /**
   * Triggered and executed immediately after calling Load.
   */
  protected abstract srvLoad(): void;

  /**
   * Triggered and last to be executed after calling Load.
   */
  protected abstract srvAfterLoad(): void;

  public abstract findById(id: string): T;

  /**
   * Triggers srvBeforeSave, srvSave and then srvAfterLoad in order.
   */
  public save(): void {
    this.srvSave();
    this.srvAfterSave();
  }

  /**
   * Triggered by Remove, executed after srvBeforeSave.
   */
  protected abstract srvSave(): void;

  /**
   * Triggered and last to be executed after calling Save.
   */
  protected abstract srvAfterSave(): void;
}
