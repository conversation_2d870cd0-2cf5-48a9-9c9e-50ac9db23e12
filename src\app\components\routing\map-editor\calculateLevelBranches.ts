let insertedLevels = new Set<string>();

export class CalculateLevelBranches 
{
    constructor(private mapPoints, private levelsFromArea, private editorPort){}


    //For more info, look the method name on the documentation.
    public initializeBranchesToDraw(linesToDraw, lineToDraw)
    {
        let points = Object.keys(this.mapPoints.points);//Level IDs
        lineToDraw = [];
        linesToDraw = [];

        let nextLevel;

        //Fill the main branch.
        for(let i = 0; i < points.length; i++)
        {
            //Insert the first level and find the next linked level.
            if(i == 0) 
            {
                lineToDraw = this.addLevelOnNewLineToDraw(lineToDraw, nextLevel, i);

                nextLevel = this.levelsFromArea[i]?.linkedLevelIds[0];
                insertedLevels.add(nextLevel);
            }

            //insert the next linked level.
            for(let j = 0; j < this.levelsFromArea.length; j++)
            {
                if(this.levelsFromArea[j]?.id == nextLevel)
                {
                    lineToDraw = this.addLevelOnNewLineToDraw(lineToDraw, nextLevel);

                    nextLevel = this.levelsFromArea[j]?.linkedLevelIds[0];
                    insertedLevels.add(nextLevel);
                    break;
                }
            }
        }

        linesToDraw.push(lineToDraw);

        //Analize each level. Used to fill the braches.
        for(let i = 0; i < this.levelsFromArea?.length; i++)
        {
            //When the level have more than one linkedlevel it means it have branches.
            if(this.levelsFromArea[i]?.linkedLevelIds.length > 1)
            {
                this.insideEachBranch(i, nextLevel, linesToDraw, false);
            }
        }

        //When there are not linked branch to the main branch
        for(let i = 0; i < this.levelsFromArea.length; i++)
        {
            if(!insertedLevels.has(this.levelsFromArea[i]?.id))
            {
                nextLevel = this.levelsFromArea[i]?.linkedLevelIds[0];
                //Get inside each branch and put them inside the array to draw. 
                this.insideEachBranch(i, nextLevel, linesToDraw, false);
            }
        }

        return linesToDraw;
    }


    private insideEachBranch(i : number, nextLevel : string, linesToDraw, startFromOne : boolean = false)
    {
         //Get inside each branch and put them inside the array to draw. (-1) beacause the first branch already was created above.
         let levelsFromAreaLength = startFromOne ? this.levelsFromArea[i]?.linkedLevelIds?.length-1 : this.levelsFromArea[i]?.linkedLevelIds?.length;

         for(let k = 0; k < levelsFromAreaLength; k++)
         {
             let newLineToDraw : any[] = [];
             //Enter inside a specific branch.
             //this.levelsFromArea.length is here because i do not know the size of the branch.
             for(let j = 0; j < this.levelsFromArea?.length; j++)
             {
                 let needBreak = false;
                 if(j == 0)
                 {
                    newLineToDraw = this.addLevelOnNewLineToDraw(newLineToDraw, nextLevel, i);

                    nextLevel = this.levelsFromArea[i]?.linkedLevelIds[startFromOne ? k+1 : k];
                    insertedLevels.add(nextLevel);
                    continue;
                 }
                 
                 //walking inside the subbranch.
                 for(let t = 0; t < this.levelsFromArea.length; t++)
                 {   
                     if(this.levelsFromArea[t]?.id == nextLevel)
                     {
                         //The last level of the branch
                         if(this.levelsFromArea[t]?.linkedLevelIds?.length == 0) 
                         {
                            newLineToDraw = this.addLevelOnNewLineToDraw(newLineToDraw, nextLevel);

                             needBreak = true;
                             break;
                         }
                         newLineToDraw = this.addLevelOnNewLineToDraw(newLineToDraw, nextLevel);
                         nextLevel = this.levelsFromArea[t]?.linkedLevelIds[0];
                         insertedLevels.add(nextLevel);
                     }
                 }
                 if(needBreak) break;
             }
             linesToDraw.push(newLineToDraw);
         }
    }

    private addLevelOnNewLineToDraw(newLineToDraw, nextLevel, index : number = -1)
    {
        let level = index != -1 ? this.levelsFromArea[index].id : nextLevel;
        newLineToDraw.push
        (
           [
               this.editorPort.positionInPixelsDeadzone(this.mapPoints?.points[level]?.position)?.xd,
               this.editorPort.positionInPixelsDeadzone(this.mapPoints?.points[level]?.position)?.yd
           ]
        );

        return newLineToDraw;
    }
}