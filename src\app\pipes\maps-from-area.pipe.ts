import { Pipe, PipeTransform } from '@angular/core';
import { MapService } from '../services/map.service';
import { Map } from '../../models/mapsys1';

@Pipe({
  name: 'mapsFromArea',
  pure: false
})
export class MapsFromAreaPipe implements PipeTransform {
  constructor(private _mapService: MapService) {}

  transform(areaId: string): Map[] {
    return this._mapService.data.filter(map => map.areaId === areaId);
  }
}
