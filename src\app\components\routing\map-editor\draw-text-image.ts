import { Canvas } from './Canvas';


export class DrawTextImage
{
    constructor(private drawCanvas: Canvas, private drawTextImageCanvas : Canvas){}

    public drawMapCanvasLevelText(xPosition, yPosition, text): void 
    {
        const levelPoint = {xd: xPosition, yd: yPosition};

        if (!levelPoint) return;
        
        const positionInPixels = this.drawCanvas.positionInPixels(levelPoint);   
        
        // Circle
        this.drawTextImageCanvas.context.beginPath();
        this.drawTextImageCanvas.context.arc(
            Math.floor(this.drawCanvas.deadZoneInPixels + positionInPixels.x),
            Math.floor(this.drawCanvas.deadZoneInPixels + positionInPixels.y),
            Math.floor(this.drawCanvas.circleRadiusInPixels),
            0, 2 * Math.PI, false);

        this.drawTextImageCanvas.context.fillStyle = 'red';
        this.drawTextImageCanvas.context.fill();
        this.drawTextImageCanvas.context.lineWidth = 4;
        this.drawTextImageCanvas.context.strokeStyle = 'black'
        this.drawTextImageCanvas.context.stroke();
        this.drawTextImageCanvas.context.closePath();

        // Text
        this.drawTextImageCanvas.context.fillStyle = 'black';
        let fontSize = this.drawTextImageCanvas.levelRadius * 4;
        fontSize = fontSize > 40 ? 40 : fontSize;

        this.drawTextImageCanvas.context.font = 30 + 'px Arial';
        this.drawTextImageCanvas.context.textAlign = 'center';
        this.drawTextImageCanvas.context.textBaseline = 'middle';
        this.drawTextImageCanvas.context.fillText(text.toString() + '',
            Math.floor(this.drawCanvas.deadZoneInPixels +  positionInPixels.x),
            Math.floor(this.drawCanvas.deadZoneInPixels +  positionInPixels.y));
    }
}