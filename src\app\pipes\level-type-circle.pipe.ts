import { Pipe, PipeTransform } from '@angular/core';
import { LevelType, LevelPoint } from '../../models/mapsys1';
import { levelTypeColor } from '../../custom/Types';

@Pipe({
  name: 'levelTypeCircle',
  pure: false,
})
export class LevelTypeCirclePipe implements PipeTransform {

  transform(levelType: LevelType, levelId: string, points: LevelPoint[], newLevelIds: string[]): unknown {
    return {
     'background-color': levelTypeColor[+levelType],
      color: 'black',
      cursor: 'pointer',
      'margin-right': '5px',
      'line-height': '40px',
     border: points[levelId] ? newLevelIds?.includes(levelId) ? '3px solid orange' : '' : '3px solid red',
    };
  }
}
