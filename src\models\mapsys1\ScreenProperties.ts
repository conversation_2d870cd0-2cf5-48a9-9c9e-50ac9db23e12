import { AnchorPoint } from './AnchorPoint';

export class PixelDimension {
    width: number;
    height: number;
  
    constructor(width?: number, height?: number) {
      this.width = width;
      this.height = height;
    }
  
    toRatio(): Ratio {
      return this.height > this.width
        ? new Ratio(this.height / this.width, 1)
        : new Ratio(1, this.width / this.height);
    }
  
    toAnchorPoint(position: { x: number; y: number }): AnchorPoint {
      return {
        xd: (position.x * 100) / this.width,
        yd: (position.y * 100) / this.height,
      };
    }
  }

  export class Ratio 
{
    a: number;
    c: number;
  
    constructor(a?: number, c?: number) 
    {
      this.a = Math.round(a * 10) / 10;
      this.c = Math.round(c * 10) / 10;
    }
  
    static deepLoad(ratio: Ratio): Ratio 
    {
      return Object.assign(new Ratio(), ratio);
    }
  
    toDimension(preset: { width: number } | { height: number }): PixelDimension 
    {
      const dimension: PixelDimension = new PixelDimension(0, 0);
      if ((preset as { width: number }).width !== undefined) 
      {
        dimension.width = (preset as { width: number }).width;
        dimension.height = (dimension.width * this.a) / this.c;
      } 
      else 
      {
        dimension.height = (preset as { height: number }).height;
        dimension.width = (dimension.height * this.c) / this.a;
      }
      // Removido limite artificial de 32000px para permitir mapas de qualquer altura
      // O canvas HTML5 suporta dimensões muito maiores que 32000px em navegadores modernos
      // Limitação real depende da memória disponível e do navegador específico

      return dimension;
    }
  
    invert(): Ratio 
    {
      const ratio = new Ratio();
      ratio.c = this.a;
      ratio.a = this.c;
      return ratio;
    }
  }
  
  export const ASPECT_RATIO_PRESETS: Ratio[] = [
    new Ratio(19.5, 9),
    new Ratio(16, 9),
    new Ratio(4, 3),
    new Ratio(3, 1),
  ];