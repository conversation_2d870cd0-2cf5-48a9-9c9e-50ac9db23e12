  <!-- START: Map Import/Export Section-->
<div class="card">
  <div class="row middle">
    <p style="font-size: 10px;">Map Import/Export File</p>
    <div class="col-md-12">
      <div class="btn-group">

        <input
          class="input-file"
          id="mps"
          type="file"
          #mpsFile
          (change)="promptImportMPS($event.target.files)"
          (click)="mpsFile.value = null"/>

        <label
          class="btn btn-sm btn-fill middle"
          [ngClass]="lastLoadedTime ? 'btn-success' : 'btn-warning'"
          title="Imports a MAPS JSON Project file"  
          for="mps">
          <i class="icon pe-7s-download"></i> Open
        </label>

        <label
          class="btn btn-sm btn-default btn-fill btn-primary middle"
          (click)="downloadMPS()"
          title="Saves as a MAPS JSON Project file">
          <i class="icon pe-7s-upload"></i>Save
        </label>

      </div>
    </div>
  </div>

  <br><br>
  <!-- END: Map Import/Export Section-->

  <!-- START: Unity Export Section-->
  <div class="row middle">
    <p style="font-size: 10px;">Unity Export File</p>
    <div class="col-md-12">
      <div class="btn-group">
        <label
          (click)="exportToUnity()"
          class="btn btn-sm btn-fill middle"
          style="background-color:rgb(54, 116, 248)"
          title="Export a Unity JSON Project file"  
          for="unity">
          <i class="icon pe-7s-upload"></i>Unity Save
        </label>

      </div>
    </div>
  </div>
</div>
<!-- END: Unity Export Section-->
