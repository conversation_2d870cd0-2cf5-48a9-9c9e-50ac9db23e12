import { AnchorPoint, ItemEvent } from '../../../../models/mapsys1'
import { Canvas } from './Canvas';

interface Position
{
    x : number;
    y : number;
}

export class SelectItem
{
    constructor(){}
    private allEventsOfLevel : ItemEvent[] = [];

    public selectItem(items : ItemEvent[], mousePosition : Position, itemCanvas : Canvas) : ItemEvent[]
    {
        this.allEventsOfLevel = [];
        let anchor : AnchorPoint = {xd : 0, yd: 0};
        //It should be the same radius of the event position
        let anchorOffset : AnchorPoint = {xd : 2, yd: 2};
        let offsetPosition : Position = itemCanvas.positionInPixels(anchorOffset, false);

        for(let i = 0; i < items.length; i++)
        {
            //25 and 1 are arbitrary numbers to put the item in 45º.
            anchor.xd = items[i].levelXPosition + 25;
            anchor.yd = items[i].levelYPosition + 1;
            let itemPosition : Position = itemCanvas.positionInPixels(anchor, false);


            if( mousePosition.x < itemPosition.x + offsetPosition.x && 
                mousePosition.x > itemPosition.x - offsetPosition.x &&
                mousePosition.y < itemPosition.y + offsetPosition.y &&
                mousePosition.y > itemPosition.y - offsetPosition.y)
            {
                this.allEventsOfLevel.push(items[i]);
            }
        }

        return this.allEventsOfLevel;
    }
}