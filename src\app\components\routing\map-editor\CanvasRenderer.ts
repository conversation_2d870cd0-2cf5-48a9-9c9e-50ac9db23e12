import { AreaService } from '../../../services/area.service';
import { Level, Area, Map } from '../../../../models/mapsys1';
import { levelTypeColor } from '../../../../custom/Types';
import { PixelDimension } from '../../../../models/mapsys1/ScreenProperties';
import { Canvas } from './Canvas';
import { DrawBackgroundImagesService } from '../../../../app/services/draw-background-images.service';
import { UserSettingsService } from '../../../../app/services/user-settings.service';

/**
 * CanvasRenderer - Classe responsável por renderizar todos os elementos visuais do mapa
 *
 * Esta classe centraliza toda a lógica de desenho do canvas do map-editor, incluindo:
 * - Renderização de níveis (círculos coloridos com números)
 * - Desenho da deadzone (área cinza ao redor do mapa com bordas diagonais)
 * - Renderização de links entre níveis (linhas tracejadas conectando círculos)
 * - Desenho de text points (círculos vermelhos) e image points (círculos azuis)
 * - Gestão de background images (imagens de fundo do mapa)
 * - Sistema de destaque visual para interações (hover, arrasto, seleção)
 * - Renderização de páginas/divisões do mapa para navegação
 * - Suporte a zoom e redimensionamento dinâmico
 *
 * Funciona com sistema de canvas único, substituindo o sistema anterior de múltiplos canvas
 * para melhor performance e simplicidade de manutenção.
 */
export class CanvasRenderer
{
    /** Flag que controla se o mapa deve ser exibido sobre imagem de fundo */
    private showMapOnBackground : boolean = false;

    /** Valor atual do zoom para cálculos de fonte responsiva */
    private _currentZoomValue: number = 100;

    /**
     * Construtor do CanvasRenderer
     * Injeta todas as dependências necessárias para renderização
     *
     * @param drawCanvas - Canvas para desenhos livres do usuário
     * @param itemCanvas - Canvas para renderização de itens especiais
     * @param _areaService - Serviço para gerenciar áreas e obter índices de níveis
     * @param _mapCanvas - Canvas principal onde o mapa é renderizado
     * @param _viewPortCanvas - Canvas para controle de viewport/visualização
     * @param _editorPortCanvas - Canvas para o editor port
     * @param _userSettingsService - Serviço para acessar configurações do usuário
     * @param _drawBackgroundImagesService - Serviço para gerenciar imagens de fundo
     */
    constructor(private drawCanvas : Canvas, private itemCanvas : Canvas, private _areaService: AreaService, private _mapCanvas: Canvas,
        private _viewPortCanvas: Canvas, private _editorPortCanvas: Canvas, private  _userSettingsService: UserSettingsService,
        private _drawBackgroundImagesService : DrawBackgroundImagesService)
    {
        // Construtor vazio - todas as dependências são injetadas via parâmetros
    }

    /**
     * Define o valor atual do zoom para cálculos de fonte responsiva
     * @param zoomValue Valor do zoom (ex: 100 = 100%)
     */
    public setZoomValue(zoomValue: number): void {
        this._currentZoomValue = zoomValue;
    }

    /**
     * Calcula o tamanho da fonte baseado no raio do círculo e zoom atual
     * Garante que o texto escale proporcionalmente com o zoom
     *
     * @param baseMultiplier Multiplicador base para o cálculo (ex: 6 para círculos principais)
     * @param maxSize Tamanho máximo permitido em zoom 100%
     * @param minSize Tamanho mínimo permitido (opcional)
     * @returns Tamanho da fonte ajustado pelo zoom
     */
    private calculateFontSize(baseMultiplier: number, maxSize: number, minSize: number = 10): number {
        const zoomFactor = this._currentZoomValue * 0.01;
        let fontSize = this._mapCanvas.levelRadius * baseMultiplier * zoomFactor;

        // Aplica limites mínimo e máximo escalados pelo zoom
        const scaledMaxSize = maxSize * zoomFactor;
        const scaledMinSize = minSize * zoomFactor;

        fontSize = Math.min(fontSize, scaledMaxSize);
        fontSize = Math.max(fontSize, scaledMinSize);

        return Math.round(fontSize);
    }

    // Draws deadzone lines and this.mapCanvas diagonals
    private drawMapCanvasDeadzone(): void
    {
        this._mapCanvas.context.strokeStyle = 'darkgrey';
        this._mapCanvas.context.lineWidth = 1; // Linha mais fina para as bordas do mapa
        this._mapCanvas.context.beginPath();

        // Top
        this._mapCanvas.context.moveTo(0, 0);
        this._mapCanvas.context.lineTo(this._mapCanvas.deadZoneInPixels, this._mapCanvas.deadZoneInPixels);

        this._mapCanvas.context.lineTo(this._mapCanvas.canvasDimension.width - this._mapCanvas.deadZoneInPixels,this._mapCanvas.deadZoneInPixels);
        this._mapCanvas.context.lineTo(this._mapCanvas.canvasDimension.width, 0);

        // Bottom
        this._mapCanvas.context.moveTo(0, this._mapCanvas.canvasDimension.height);
        this._mapCanvas.context.lineTo(this._mapCanvas.deadZoneInPixels, this._mapCanvas.canvasDimension.height - this._mapCanvas.deadZoneInPixels);
        this._mapCanvas.context.lineTo(this._mapCanvas.canvasDimension.width - this._mapCanvas.deadZoneInPixels,
            this._mapCanvas.canvasDimension.height - this._mapCanvas.deadZoneInPixels);
        this._mapCanvas.context.lineTo(this._mapCanvas.canvasDimension.width, this._mapCanvas.canvasDimension.height);

        // Left
        this._mapCanvas.context.moveTo(this._mapCanvas.deadZoneInPixels, this._mapCanvas.deadZoneInPixels);
        this._mapCanvas.context.lineTo(this._mapCanvas.deadZoneInPixels, this._mapCanvas.canvasDimension.height - this._mapCanvas.deadZoneInPixels);

        //Right
        this._mapCanvas.context.moveTo(this._mapCanvas.canvasDimension.width - this._mapCanvas.deadZoneInPixels, this._mapCanvas.deadZoneInPixels);
        this._mapCanvas.context.lineTo(this._mapCanvas.canvasDimension.width - this._mapCanvas.deadZoneInPixels,
            this._mapCanvas.canvasDimension.height - this._mapCanvas.deadZoneInPixels);

        this._mapCanvas.context.stroke();
        this._mapCanvas.context.closePath();
    }

    /**
     * Desenha as linhas tracejadas que conectam os círculos principais
     * Ignora explicitamente círculos vermelhos (text) e azuis (image)
     */
    private drawMapCanvasLinks(map: Map, area: Area, levelsFromArea: Level[], textPoints: any[]): void
    {
        // Cria um conjunto de IDs de pontos de texto/imagem para verificação rápida
        const textImageIds = new Set();
        if (textPoints && textPoints.length > 0) {
            textPoints.forEach(point => {
                if (point && point.id) {
                    textImageIds.add(point.id);
                }
            });
        }

        levelsFromArea.forEach((level) =>
        {
            // Ignora se este nível for um ponto de texto/imagem
            if (textImageIds.has(level.id)) return;

            const levelPoint = map.points[level.id];
            if (!levelPoint) return;

            const levelPositionInPixels = this._mapCanvas.positionInPixels(levelPoint.position);

            level.linkedLevelIds.filter((linkedLevelId) => {
                // Filtra apenas níveis da mesma área E que não sejam pontos de texto/imagem
                return area.id === Area.getSubIdFrom(linkedLevelId) && !textImageIds.has(linkedLevelId);
            }).forEach((linkedLevelId) =>
            {
                const linkPoint = map.points[linkedLevelId];
                if (!linkPoint) return;

                const linkPositionInPixels = this._mapCanvas.positionInPixels(linkPoint.position);

                this._mapCanvas.context.strokeStyle = '#999999'; // Cor mais sutil para as conexões
                this._mapCanvas.context.beginPath();
                this._mapCanvas.context.lineWidth = 2; // Linha mais fina para conexões entre círculos
                // Padrão pontilhado sutil como na imagem: pontos pequenos com espaços pequenos
                this._mapCanvas.context.setLineDash([2, 4]); // Pontos de 2px com espaços de 4px

                this._mapCanvas.context.moveTo(
                    Math.floor(this._mapCanvas.deadZoneInPixels + levelPositionInPixels.x),
                    Math.floor(this._mapCanvas.deadZoneInPixels + levelPositionInPixels.y)
                );
                this._mapCanvas.context.lineTo(
                    Math.floor(this._mapCanvas.deadZoneInPixels + linkPositionInPixels.x),
                    Math.floor(this._mapCanvas.deadZoneInPixels + linkPositionInPixels.y)
                );
                this._mapCanvas.context.stroke();
                this._mapCanvas.context.closePath();
                this._mapCanvas.context.setLineDash([]);
            });
        });
    }

    private drawMapCanvasLevel(map: Map, level: Level, detectedLevelId: string | undefined, newLevelIds: string[]): void
    {

        const levelPoint = map.points[level.id];

        if (!levelPoint) return;
        
        const positionInPixels = this._mapCanvas.positionInPixels(levelPoint.position);

        const isSelected = detectedLevelId === level.id;        

       // === DESENHO DO CÍRCULO ===
        this._mapCanvas.context.beginPath();

        // === CONFIGURAÇÃO DA SOMBRA ===
        this._mapCanvas.context.shadowColor = '#333';
        this._mapCanvas.context.shadowBlur = 4;
        this._mapCanvas.context.shadowOffsetX = 2;
        this._mapCanvas.context.shadowOffsetY = 2;

        this._mapCanvas.context.arc(
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.x),
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.y),
            Math.floor(this._mapCanvas.circleRadiusInPixels),
            0, 2 * Math.PI, false);

        // Preenche o círculo com a cor baseada no tipo
        this._mapCanvas.context.fillStyle = levelTypeColor[+level.type];
        this._mapCanvas.context.fill();

        // ✅ BORDA BRANCA PADRÃO + DESTAQUE AMARELO: Aplica borda branca por padrão, amarela quando detectado
        this._mapCanvas.context.lineWidth = detectedLevelId === level.id ? 4 : 2.5; // Borda mais grossa quando detectado, igual aos outros círculos
        this._mapCanvas.context.strokeStyle = detectedLevelId === level.id ? 'yellow' : newLevelIds.includes(level.id) ? 'orange' : '#fff';
        this._mapCanvas.context.stroke();

        // === LIMPEZA DO EFEITO DE SOMBRA ===
        // Remove a sombra para não afetar outros elementos
        this._mapCanvas.context.shadowBlur = 0;
        this._mapCanvas.context.shadowOffsetX = 0;
        this._mapCanvas.context.shadowOffsetY = 0;

        this._mapCanvas.context.closePath();

        // === TEXTO RESPONSIVO AO ZOOM ===
        this._mapCanvas.context.fillStyle = '#fff';

        // Calcula tamanho da fonte responsivo ao zoom
        // Usa multiplicador 6 para círculos principais, máximo 27px em zoom 100%
        const fontSize = this.calculateFontSize(12, 27, 17);

        this._mapCanvas.context.font = fontSize + 'px Arial';
        this._mapCanvas.context.textAlign = 'center';
        this._mapCanvas.context.textBaseline = 'middle';
        this._mapCanvas.context.fillText(this._areaService.getLevelIndex(level.id) + '',
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.x),
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.y));
       
    }

    private drawMapCanvasLevelText(xPosition, yPosition, text, detectedTextPointId?: string): void
    {
        const levelPoint = {xd: xPosition, yd: yPosition};

        if (!levelPoint) return;

        const positionInPixels = this._mapCanvas.positionInPixels(levelPoint);
        const isDetected = detectedTextPointId === text;

        // Circle
        this._mapCanvas.context.beginPath();

        // === CONFIGURAÇÃO DA SOMBRA ===
        this._mapCanvas.context.shadowColor = '#333';
        this._mapCanvas.context.shadowBlur = 4;
        this._mapCanvas.context.shadowOffsetX = 2;
        this._mapCanvas.context.shadowOffsetY = 2;

        this._mapCanvas.context.arc(
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.x),
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.y),
            Math.floor(this._mapCanvas.circleRadiusInPixels),
            0, 2 * Math.PI, false);

        this._mapCanvas.context.fillStyle = 'red';
        this._mapCanvas.context.fill();

        // ✅ BORDA BRANCA PADRÃO + DESTAQUE VISUAL: Aplica borda branca por padrão, amarela quando detectado
        this._mapCanvas.context.lineWidth = isDetected ? 4 : 2.5; // Borda mais grossa quando detectado, igual aos outros círculos
        this._mapCanvas.context.strokeStyle = isDetected ? 'yellow' : '#fff'; // Borda branca padrão, amarela para destaque
        this._mapCanvas.context.stroke();

        // === LIMPEZA DO EFEITO DE SOMBRA ===
        this._mapCanvas.context.shadowBlur = 0;
        this._mapCanvas.context.shadowOffsetX = 0;
        this._mapCanvas.context.shadowOffsetY = 0;

        this._mapCanvas.context.closePath();

        // === TEXTO RESPONSIVO AO ZOOM (CÍRCULOS VERMELHOS) ===
        this._mapCanvas.context.fillStyle = 'white';

        // Calcula tamanho da fonte responsivo ao zoom para círculos vermelhos
        // Usa multiplicador 4 para círculos menores, máximo 30px em zoom 100%
        const fontSize = this.calculateFontSize(10, 25, 15);

        this._mapCanvas.context.font = fontSize + 'px Arial';
        this._mapCanvas.context.textAlign = 'center';
        this._mapCanvas.context.textBaseline = 'middle';
        this._mapCanvas.context.fillText(text.toString() + '',
            Math.floor(this._mapCanvas.deadZoneInPixels +  positionInPixels.x),
            Math.floor(this._mapCanvas.deadZoneInPixels +  positionInPixels.y));
    }


   private drawMapCanvasLevelImage(xPosition, yPosition, text, detectedTextPointId?: string): void
    {
        const levelPoint = {xd: xPosition, yd: yPosition};

        if (!levelPoint) return;

        const positionInPixels = this._mapCanvas.positionInPixels(levelPoint);
        const isDetected = detectedTextPointId === text;

        // Circle
        this._mapCanvas.context.beginPath();

        // === CONFIGURAÇÃO DA SOMBRA ===
        this._mapCanvas.context.shadowColor = '#333';
        this._mapCanvas.context.shadowBlur = 4;
        this._mapCanvas.context.shadowOffsetX = 2;
        this._mapCanvas.context.shadowOffsetY = 2;

        this._mapCanvas.context.arc(
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.x) ,
            Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.y) ,
            Math.floor(this._mapCanvas.circleRadiusInPixels),
            0, 2 * Math.PI,false);

        this._mapCanvas.context.fillStyle = 'blue';
        this._mapCanvas.context.fill();

        // ✅ BORDA BRANCA PADRÃO + DESTAQUE VISUAL: Aplica borda branca por padrão, amarela quando detectado
        this._mapCanvas.context.lineWidth = isDetected ? 4 : 2.5; // Borda mais grossa quando detectado, igual aos outros círculos
        this._mapCanvas.context.strokeStyle = isDetected ? 'yellow' : '#fff'; // Borda branca padrão, amarela para destaque
        this._mapCanvas.context.stroke();

        // === LIMPEZA DO EFEITO DE SOMBRA ===
        this._mapCanvas.context.shadowBlur = 0;
        this._mapCanvas.context.shadowOffsetX = 0;
        this._mapCanvas.context.shadowOffsetY = 0;

        this._mapCanvas.context.closePath();

        // === TEXTO RESPONSIVO AO ZOOM (CÍRCULOS AZUIS) ===
        this._mapCanvas.context.fillStyle = 'white';

        // Calcula tamanho da fonte responsivo ao zoom para círculos azuis
        // Usa multiplicador 4 para círculos menores, máximo 27px em zoom 100%
        const fontSize = this.calculateFontSize(6, 18, 12);

        this._mapCanvas.context.font = fontSize + 'px Arial';
        this._mapCanvas.context.textAlign = 'center';
        this._mapCanvas.context.textBaseline = 'middle';
        this._mapCanvas.context.fillText(text.toString() + '',
            Math.floor(this._mapCanvas.deadZoneInPixels +  positionInPixels.x),
            Math.floor(this._mapCanvas.deadZoneInPixels +  positionInPixels.y));
    }




    /**
     * Calcula uma posição visível para círculos vermelhos/azuis
     * Se estiver sobreposto a um círculo principal, desloca para posição próxima
     */
    private getVisiblePosition(originalX: number, originalY: number, mainCirclePositions: Array<{x: number, y: number}>): {x: number, y: number} {
        // Converte a posição original para pixels
        const originalPixelPos = this._mapCanvas.positionInPixels({xd: originalX, yd: originalY});
        const originalPixelX = Math.floor(this._mapCanvas.deadZoneInPixels + originalPixelPos.x);
        const originalPixelY = Math.floor(this._mapCanvas.deadZoneInPixels + originalPixelPos.y);

        const circleRadius = this._mapCanvas.circleRadiusInPixels;
        const minDistance = circleRadius * 2.2; // Distância mínima para considerar sobreposição

        // Verifica se está sobreposto a algum círculo principal
        const isOverlapping = mainCirclePositions.some(mainPos => {
            const distance = Math.sqrt(
                Math.pow(originalPixelX - mainPos.x, 2) +
                Math.pow(originalPixelY - mainPos.y, 2)
            );
            return distance < minDistance;
        });

        // Se não está sobreposto, retorna a posição original
        if (!isOverlapping) {
            return {x: originalX, y: originalY};
        }

        // Se está sobreposto, encontra uma posição próxima mas visível
        const offsetDistance = circleRadius * 2.5; // Distância do deslocamento
        const angles = [0, Math.PI/4, Math.PI/2, 3*Math.PI/4, Math.PI, 5*Math.PI/4, 3*Math.PI/2, 7*Math.PI/4]; // 8 direções

        for (const angle of angles) {
            const newPixelX = originalPixelX + Math.cos(angle) * offsetDistance;
            const newPixelY = originalPixelY + Math.sin(angle) * offsetDistance;

            // Verifica se a nova posição está dentro dos limites do mapa
            if (this.isWithinMapBounds(newPixelX, newPixelY)) {
                // Verifica se a nova posição não está sobreposta a outro círculo principal
                const isNewPosOverlapping = mainCirclePositions.some(mainPos => {
                    const distance = Math.sqrt(
                        Math.pow(newPixelX - mainPos.x, 2) +
                        Math.pow(newPixelY - mainPos.y, 2)
                    );
                    return distance < minDistance;
                });

                if (!isNewPosOverlapping) {
                    // Converte de volta para coordenadas percentuais
                    const pixelPosWithoutDeadZone = {
                        xd: newPixelX - this._mapCanvas.deadZoneInPixels,
                        yd: newPixelY - this._mapCanvas.deadZoneInPixels
                    };
                    const mapPosition = this._mapCanvas.fromPixelsToPercentage(pixelPosWithoutDeadZone, false);
                    return {x: mapPosition.xd, y: mapPosition.yd};
                }
            }
        }

        // Se não encontrou posição livre, retorna a original (será parcialmente visível)
        return {x: originalX, y: originalY};
    }

    /**
     * Verifica se uma posição está dentro dos limites do mapa
     */
    private isWithinMapBounds(x: number, y: number): boolean {
        const margin = this._mapCanvas.circleRadiusInPixels * 1.5;
        return x >= this._mapCanvas.deadZoneInPixels + margin &&
               x <= this._mapCanvas.canvasDimension.width - this._mapCanvas.deadZoneInPixels - margin &&
               y >= this._mapCanvas.deadZoneInPixels + margin &&
               y <= this._mapCanvas.canvasDimension.height - this._mapCanvas.deadZoneInPixels - margin;
    }

    /**
     * Conta e desenha a quantidade de círculos em cada tela (lado esquerdo)
     */
    private drawCircleCountPerScreen(map: Map, levelsFromArea: Level[], viewPortDimension: any, timesDisplayFitsInMap: number, offsetBoundaries: any): void
    {
        // === CONFIGURAÇÃO DO TEXTO RESPONSIVO PARA CONTAGEM DE CÍRCULOS ===
        this._mapCanvas.context.fillStyle = '#888888'; // Cor cinza para os números

        // Calcula tamanho da fonte responsivo ao zoom para números de contagem
        const fontSize = this.calculateFontSize(10, 25, 15); // Multiplicador menor para textos auxiliares
        this._mapCanvas.context.font = fontSize + 'px Arial';
        this._mapCanvas.context.textAlign = 'center';
        this._mapCanvas.context.textBaseline = 'middle';

        // Conta círculos apenas para divisões verticais (altura > largura)
        if (this._mapCanvas.dimension.height > viewPortDimension.height) {
            const offset = this._mapCanvas.dimension.height - viewPortDimension.height * timesDisplayFitsInMap;

            for (let i = 0; i < timesDisplayFitsInMap; i++) {
                let circleCount = 0;

                // Conta círculos nesta seção
                levelsFromArea.forEach((level) => {
                    const point = map.points[level.id];
                    if (!point) return;

                    const posY = this._mapCanvas.positionInPixels(point.position, false).y + this._mapCanvas.deadZoneInPixels;

                    if (posY >= offset + offsetBoundaries.minY + viewPortDimension.height * i &&
                        posY < offset + offsetBoundaries.minY + viewPortDimension.height * (i + 1)) {
                        circleCount++;
                    }
                });

                // Calcula a posição Y central da seção
                const sectionCenterY = offset + offsetBoundaries.minY + viewPortDimension.height * i + (viewPortDimension.height * 0.5);

                // Posição X na área do dead zone (lado ESQUERDO)
                const leftDeadZoneX = this._mapCanvas.deadZoneInPixels * 0.5;

                // Desenha a contagem de círculos no lado esquerdo
                this._mapCanvas.context.fillText(
                    circleCount.toString(),
                    Math.floor(leftDeadZoneX),
                    Math.floor(sectionCenterY)
                );
            }
        }
    }

    /**
     * Desenha os números das telas na área do dead zone (lado direito)
     * Numeração começa de baixo para cima (1, 2, 3...)
     * Lado esquerdo é reservado para contagem de círculos
     */
    private drawScreenNumbers(viewPortDimension: any, timesDisplayFitsInMap: number, offsetBoundaries: any): void
    {
        // === CONFIGURAÇÃO DO TEXTO RESPONSIVO PARA NÚMEROS DAS TELAS ===
        this._mapCanvas.context.fillStyle = '#666666'; // Cor cinza para os números

        // Calcula tamanho da fonte responsivo ao zoom para números das telas
        const fontSize = this.calculateFontSize(10, 25, 15); // Multiplicador menor para textos auxiliares
        this._mapCanvas.context.font = fontSize + 'px Arial';
        this._mapCanvas.context.textAlign = 'center';
        this._mapCanvas.context.textBaseline = 'middle';

        // Desenha números apenas para divisões verticais (altura > largura)
        if (this._mapCanvas.dimension.height > viewPortDimension.height) {
            const offset = this._mapCanvas.dimension.height - viewPortDimension.height * timesDisplayFitsInMap;

            for (let i = 0; i < timesDisplayFitsInMap; i++) {
                // Calcula o número da tela (começando de baixo para cima)
                const screenNumber = timesDisplayFitsInMap - i;

                // Calcula a posição Y central da seção
                const sectionCenterY = offset + offsetBoundaries.minY + viewPortDimension.height * i + (viewPortDimension.height * 0.5);

                // Posição X na área do dead zone (lado DIREITO)
                // Calcula a posição no lado direito da área do dead zone
                const rightDeadZoneX = this._mapCanvas.canvasDimension.width - (this._mapCanvas.deadZoneInPixels * 0.5);

                // Desenha o número da tela no lado direito
                this._mapCanvas.context.fillText(
                    screenNumber.toString(),
                    Math.floor(rightDeadZoneX),
                    Math.floor(sectionCenterY)
                );
            }
        }
    }

    private drawMapCanvasPages(map: Map, levelsFromArea: Level[]): void
    {
        const viewPortDimension = this._viewPortCanvas.matchDimension(this._mapCanvas.dimension, false, this._mapCanvas.layout);

        let timesDisplayFitsInMap = Math.floor(
            this._mapCanvas.dimension.height > viewPortDimension.height ?
            this._mapCanvas.dimension.height / viewPortDimension.height + 1 :
            this._mapCanvas.dimension.width / viewPortDimension.width + 1);

        timesDisplayFitsInMap = viewPortDimension.width >= this._mapCanvas.dimension.width &&
                viewPortDimension.height >= this._mapCanvas.dimension.height ? 0 : timesDisplayFitsInMap;

        this._mapCanvas.context.strokeStyle = '#ff6b6b'; // Cor vermelha mais suave para as linhas divisórias
        this._mapCanvas.context.fillStyle = 'grey';
        this._mapCanvas.context.font = '50px Arial';
        this._mapCanvas.context.textAlign = 'center';
        this._mapCanvas.context.textBaseline = 'middle';

        // Calcula offsetBoundaries uma vez para usar em ambos os métodos
        const offsetBoundaries = this.offsetBoundaries(this._mapCanvas.dimension, viewPortDimension);
        offsetBoundaries.minY += this._mapCanvas.deadZoneInPixels;
        offsetBoundaries.maxY += this._mapCanvas.deadZoneInPixels;
        offsetBoundaries.minX += this._mapCanvas.deadZoneInPixels;
        offsetBoundaries.maxX += this._mapCanvas.deadZoneInPixels;

        // Desenha a contagem de círculos no lado esquerdo
        this.drawCircleCountPerScreen(map, levelsFromArea, viewPortDimension, timesDisplayFitsInMap, offsetBoundaries);

        // Desenha os números das telas na área do dead zone (lado direito)
        this.drawScreenNumbers(viewPortDimension, timesDisplayFitsInMap, offsetBoundaries);

        // Desenha as linhas divisórias
        for (let i = 0; i < timesDisplayFitsInMap; i++)
        {
            this._mapCanvas.context.beginPath();
            let offset = 0;
            this._mapCanvas.context.lineWidth = 1.5; // Linha mais fina para divisões do device

            // Padrão tracejado mais sutil como na imagem: traços pequenos com espaços pequenos
            this._mapCanvas.context.setLineDash(i === timesDisplayFitsInMap - 1 ? [0] : [8, 8]);

            if (this._mapCanvas.dimension.height > viewPortDimension.height)
            {
                // Desenha linhas horizontais para divisões verticais
                offset = this._mapCanvas.dimension.height - viewPortDimension.height * timesDisplayFitsInMap;
                this._mapCanvas.context.moveTo(
                    offsetBoundaries.minX + viewPortDimension.width,
                    offset + offsetBoundaries.minY + viewPortDimension.height * i
                );
                this._mapCanvas.context.lineTo(
                    offsetBoundaries.minX,
                    offset + offsetBoundaries.minY + viewPortDimension.height * i
                );
                this._mapCanvas.context.stroke();
            }
            else
            {
                // Desenha linhas verticais para divisões horizontais
                offset = this._mapCanvas.dimension.width - viewPortDimension.width * timesDisplayFitsInMap;
                this._mapCanvas.context.moveTo(
                    offset + offsetBoundaries.minX + viewPortDimension.width * i,
                    offsetBoundaries.minY + viewPortDimension.height
                );
                this._mapCanvas.context.lineTo(
                    offset + offsetBoundaries.minX + viewPortDimension.width * i,
                    offsetBoundaries.minY
                );
                this._mapCanvas.context.stroke();
            }
            this._mapCanvas.context.closePath();
        }
    }

    drawMapCanvas(map: Map, area: Area, levelsFromArea: Level[],
        detectedLevelId: string | undefined, newLevelIds: string[], textPoints, isShowText, isShowImage, detectedTextPointId?: string): void
    {
        // === LIMPEZA COMPLETA DO CANVAS ===
        // Limpa todo o canvas incluindo a deadzone para remover qualquer rastro/sombra
        this._mapCanvas.context.clearRect(0, 0,
            this._mapCanvas.canvasDimension.width, this._mapCanvas.canvasDimension.height);

        if(this.showMapOnBackground)
            this._drawBackgroundImagesService.drawImageOnBackground(area, this._mapCanvas);

        this.drawMapCanvasDeadzone();

        // Passa textPoints para evitar conexões com círculos vermelhos/azuis
        this.drawMapCanvasLinks(map, area, levelsFromArea, textPoints);

        // === DESENHO COM DETECÇÃO DE SOBREPOSIÇÃO ===
        // Primeiro coleta as posições dos círculos principais
        const mainCirclePositions: Array<{x: number, y: number}> = [];
        levelsFromArea.forEach((level) => {
            const point = map.points[level.id];
            if (point) {
                const positionInPixels = this._mapCanvas.positionInPixels(point.position);
                mainCirclePositions.push({
                    x: Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.x),
                    y: Math.floor(this._mapCanvas.deadZoneInPixels + positionInPixels.y)
                });
            }
        });

        // Desenha círculos vermelhos/azuis com ajuste de posição se necessário
        if(isShowText || isShowImage) {
            if(isShowText) {
                textPoints.forEach((point) => {
                    if(point.classification === 'text') {
                        const adjustedPos = this.getVisiblePosition(point.xPosition, point.yPosition, mainCirclePositions);
                        this.drawMapCanvasLevelText(adjustedPos.x, adjustedPos.y, point.id, detectedTextPointId);
                    }
                });
            }

            if(isShowImage) {
                textPoints.forEach((point) => {
                    if(point.classification === 'image') {
                        const adjustedPos = this.getVisiblePosition(point.xPosition, point.yPosition, mainCirclePositions);
                        this.drawMapCanvasLevelImage(adjustedPos.x, adjustedPos.y, point.id, detectedTextPointId);
                    }
                });
            }
        }

        // Desenha os círculos principais por cima
        levelsFromArea.forEach((level) => {
            this.drawMapCanvasLevel(map, level, detectedLevelId, newLevelIds);
        });

        this.drawMapCanvasPages(map, levelsFromArea);

    }

    public showMapInBackground()
    {
        this.showMapOnBackground = !this.showMapOnBackground;
    }


    private offsetBoundaries(canvasDimension: PixelDimension, frame: PixelDimension): {maxX: number; minX: number; maxY: number;minY: number;} 
    {
        return { maxX: canvasDimension.width > frame.width ? -(canvasDimension.width - frame.width) : 0,
            minX: 0,
            maxY: canvasDimension.height > frame.height ? -(canvasDimension.height - frame.height) : 0,
            minY: 0,
        };
    }

    public getNewCanvasDimensions(zoomValue: number) : {x:number, y:number}
    {
        return {
            x:Math.floor(this._mapCanvas.dimension.width * (zoomValue * 0.01)),
            y:Math.floor(this._mapCanvas.dimension.width * (zoomValue * 0.01))
        }
    }

    public updateEditorPort(zoomValue: number): void 
    {
        this._editorPortCanvas.redimension(
            new PixelDimension(Math.floor(this._mapCanvas.dimension.width * (zoomValue * 0.01)),
             Math.floor(this._mapCanvas.dimension.height * (zoomValue * 0.01))
            ), true);


        this._editorPortCanvas.context.clearRect(
            0, 0,
            Math.floor(this._editorPortCanvas.canvasDimension.width),
            Math.floor(this._editorPortCanvas.canvasDimension.height));

        this._editorPortCanvas.context.drawImage(
            this._mapCanvas.el, 0, 0,
            Math.floor(this._mapCanvas.canvasDimension.width),
            Math.floor(this._mapCanvas.canvasDimension.height),
            0, 0,
            Math.floor(this._editorPortCanvas.canvasDimension.width),
            Math.floor(this._editorPortCanvas.canvasDimension.height));
    }

    public updateDrawCanvas(zoomValue: number): void 
    {
        this.drawCanvas.redimension(
            new PixelDimension(Math.floor(this._mapCanvas.dimension.width * (zoomValue * 0.01)),
             Math.floor(this._mapCanvas.dimension.height * (zoomValue * 0.01))
            ), true);


        this.drawCanvas.context.clearRect(
            0, 0,
            Math.floor(this.drawCanvas.canvasDimension.width),
            Math.floor(this.drawCanvas.canvasDimension.height));

        this.drawCanvas.context.drawImage(
            this.drawCanvas.el, 0, 0,
            Math.floor(this._mapCanvas.canvasDimension.width),
            Math.floor(this._mapCanvas.canvasDimension.height),
            0, 0,
            Math.floor(this.drawCanvas.canvasDimension.width),
            Math.floor(this.drawCanvas.canvasDimension.height));
    }

    public updateItemCanvas(zoomValue: number): void 
    {
        this.itemCanvas.redimension(
            new PixelDimension(Math.floor(this._mapCanvas.dimension.width * (zoomValue * 0.01)),
             Math.floor(this._mapCanvas.dimension.height * (zoomValue * 0.01))
            ), true);


        this.itemCanvas.context.clearRect(
            0, 0,
            Math.floor(this.itemCanvas.canvasDimension.width),
            Math.floor(this.itemCanvas.canvasDimension.height));

        this.itemCanvas.context.drawImage(
            this.itemCanvas.el, 0, 0,
            Math.floor(this.itemCanvas.canvasDimension.width),
            Math.floor(this.itemCanvas.canvasDimension.height),
            0, 0,
            Math.floor(this.itemCanvas.canvasDimension.width),
            Math.floor(this.itemCanvas.canvasDimension.height));
    }

    updateViewPort(viewPortPivot: { x: number; y: number }, viewPortOffsetPosition: { x: number; y: number }): void 
    {
        const context = this._viewPortCanvas.el.getContext('2d', { alpha: false });
        const resizedMapCanvas = this._mapCanvas.matchDimension(this._viewPortCanvas.canvasDimension, true);

        this._viewPortCanvas.context.clearRect(0, 0,Math.floor(this._viewPortCanvas.canvasDimension.width),
            Math.floor(this._viewPortCanvas.canvasDimension.height));
        
        context?.drawImage(this._mapCanvas.el,this._mapCanvas.deadZoneInPixels, this._mapCanvas.deadZoneInPixels,
            this._mapCanvas.dimension.width, this._mapCanvas.dimension.height,
            Math.floor(viewPortPivot.x + viewPortOffsetPosition.x), Math.floor(viewPortPivot.y + viewPortOffsetPosition.y),
            resizedMapCanvas.width, resizedMapCanvas.height);
    }
}
