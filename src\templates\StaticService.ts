import { ExportablePackage } from './ExportablePackage';
import { ExportableClass, Level } from '../models/mapsys1';
import { getInputList, setData, getData, setFilterValue, getFilterValue, getLastModified, setLastModified} from '../custom/others';
import { Alert } from '../custom/Alert';
import { ServiceLifeCycle } from './ServiceLifeCycle';
import { Subject } from 'rxjs';
import { File } from '../custom/File';

/**
 * Basic class template for a service that manages arrays of data for exportable classes.
 */
export abstract class StaticService<T extends ExportableClass> extends ServiceLifeCycle<T> 
{
  /**
   * An array that stores exportable class objects.
   */
  public data: T[] = [];

  /**
   * An object that stores the last accessed object from the service
   */
  private _lastModifiedId: string;
  public lastIdModification: Subject<string> = new Subject<string>();

  public set lastModifiedId(id: string) {
    this._lastModifiedId = id;
    this.lastIdModification.next(id);
    setLastModified(id, this.typeName);
  }
  public get lastModifiedId(): string {
    if (!this._lastModifiedId) {
      this._lastModifiedId = getLastModified(this.typeName);
    }
    return this._lastModifiedId;
  }

  /**
   * Stores the index of the next object
   */
  protected _nextIndex: number;

  /**
   * Creates an instance of a Service and automatically loads data from the Local Storage
   *
   * @param typeName The name of the Service type (T)
   * @param srtLstParameter The name of the data object property which will be used to sort the data when saving it
   */
  public constructor(
    /**
     * A function that returns an empty object of type T.
     */
    private deepLoad: (obj: T) => T,
    /**
     * Name of the type of object that this service manages
     * @example 'Character'
     */
    public readonly typeName: string
  ) {
    super(typeName);
    this.load();
  }

  private _pathSuffix: string = File.MPS_SUFFIX_PATH;

  set pathSuffix(pathSuffix: string) 
  {
    if (this._pathSuffix !== pathSuffix) 
    {
      if (pathSuffix === File.DSA_SUFFIX_PATH) 
      {
        this.reset(true);
      }
      this.data = [];
      this.lastModifiedId = undefined;
      this._pathSuffix = pathSuffix;
      this.load();
    }
  }

  get pathSuffix(): string 
  {
    return this._pathSuffix;
  }

  /**
   * Sets an array of data in the Local Storage
   */
  protected setSRVData(data: T[]): void 
  {
    setData(data, this.typeName, this._pathSuffix);
  }
  /**
   * Retrieves an array of data from the Local Storage
   */
  protected getSRVData(): T[] 
  {
    return getData(this.typeName, this._pathSuffix) || [];
  }

  /**
   * Sets data and removedData to an emoty array
   */
  public reset(forceLocalStorageReset?: boolean): void 
  {
    if (this._pathSuffix === File.MPS_SUFFIX_PATH || forceLocalStorageReset) 
    {
      this.data = [];
      this.lastModifiedId = undefined;
      this.srvSave();
    }
  }

  /**
   * Imports data from an ExportablePackage and automatically saves it in the Local Storage
   * @todo because this already imports the data and assigns it to the object,
   * there is no need for deep object copy from the Import Component
   *
   * @param dataPackage The ExportablePackage to be imported and managed by the Service.
   */
  public importPackage(dataPackage: ExportablePackage<T>): void 
  {
    this.reset();
    dataPackage.data.forEach((obj) => 
    {
      this.data.push(this.deepLoad(obj));
    });
    this.srvAfterLoad();
    this._nextIndex = dataPackage.nextIndex || this.data.length;
    this.srvSave();
  }

  /**
   * Exports data into an ExportablePackage
   *
   * @param exportedDateTime A string of the exported date time for the ExportedPackage versioning.
   * @param version A string of the major version for the ExportedPackage versioning.
   *
   * @returns An ExportablePackage with Exportable Class game data.
   */
  public exportPackage(
    exportedDateTime: string,
    version: string
  ): ExportablePackage<T> {
    const dataPackage = new ExportablePackage<T>(
      exportedDateTime,
      version,
      this.data,
      [],
      this._nextIndex
    );
    return dataPackage;
  }

  protected srvLoad(): void {
    this.getSRVData().forEach((obj) => {
      this.data.push(this.deepLoad(obj));
    });
  }

  protected srvAfterLoad(): void {
    this._nextIndex = getFilterValue(this.typeName, 'nextIndex');
  }

  protected srvSave(): void {
    this.setSRVData(this.data);
    setFilterValue(this._nextIndex, this.typeName, 'nextIndex');
  }

  // fetch data methods
  /**
   * Finds the index of an object within the data array and returns it
   * @param id The id of the object which will be searched
   */
  public indexOfId(id: string): number {
    const obj = this.findById(id);
    if (!obj) {
      return;
    }
    const index = this.data.indexOf(obj);
    return index === -1 ? undefined : index;
  }

  /**
   * Filters the data array return objects which contains a certain string location on the ID
   * @example
   * // data = [{id:'A1.L1'}, {id:'A2.L2'}, {id:'A2.L3'}]
   * // returns [{id:'A2.L2'}, {id:'A2.L3'}]
   * filterByLocation('A2');
   * @param id The id of the object which will be searched
   */
  public filterByLocation(locationId: string): T[] {
    return this.data.filter((o) => o.id.includes(locationId + '.'));
  }
  public cloneByLocation(locationId: string): T[] {
    const objs: T[] = [];
    this.data
      .filter((o) => o.id.includes(locationId + '.'))
      .forEach((o) => {
        objs.push(this.clone(o));
      });
    return objs;
  }

  /**
   * Filters the data array by IDs and returns it
   * @param ids IDs of the objects to be filtered
   */
  public filterByIds(ids: string[], inOrderOfIdString?: boolean): T[] 
  {
    if (!inOrderOfIdString) 
    {
      return this.data.filter((obj) => ids?.includes(obj.id));
    } 
    else 
    {
      const objs: T[] = [];
      ids.forEach((id) => 
      {
        const obj = this.data.find((o) => o.id === id);
        if (!obj) return;
        objs.push(obj);
      });
      return objs;
    }
  }

  /**
   * Clones the data array by IDs and returns it
   * @param ids IDs of the objects to be filtered
   */
  public cloneByIds(ids: string[], inOrderOfIdString?: boolean): T[] {
    const objs: T[] = [];
    if (!inOrderOfIdString) {
      this.data
        .filter((obj) => ids.includes(obj.id))
        .forEach((o) => {
          objs.push(this.clone(o));
        });
    } else {
      ids.forEach((id) => {
        const obj = this.findById(id);
        if (!obj) {
          return;
        }
        objs.push(this.clone(obj));
      });
    }
    return objs;
  }

  /**
   * Finds an object with a specific ID and returns it
   * @param ids ID of the object to be searched
   */
  public findById(id: string): T {
     return this.data.find((o) => o.id === id)
  }
/* o.id === id */
 
  /**
   * Clones an exact copy of the object but without the array reference by a specific ID and returns it
   * Use this instead of findById on lists where the user can modify the object and call
   * LifeCycleHooks.ServiceLifeCycle.modify method to replace the new object to the correspoding object with same id.
   * @param ids ID of the object to be searched
   * @see LifeCycleHooks.ServiceLifeCycle.modify
   */
  public cloneById(id: string): T {
    const obj = this.findById(id);
    if (!obj) {
      return;
    }
    return this.clone(obj);
  }

  public clone(obj: T): T {
    return this.deepLoad(JSON.parse(JSON.stringify(obj)));
  }

  public async selectId(
    orderByParameter: boolean,
    parameters: string[],
    customData?: T[]
  ): Promise<string> {
    const inputList = customData
      ? getInputList(customData, 'id', parameters, orderByParameter)
      : getInputList(this.data, 'id', parameters, orderByParameter);
    inputList.undefined = 'Undefined';
    return await Alert.showDropdown('Select ' + this.typeName, inputList).then(
      (res) => {
        return res;
      }
    );
  }

  protected srvAfterSave(): void {}
}
