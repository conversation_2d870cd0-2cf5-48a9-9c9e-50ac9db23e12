import { Injectable } from '@angular/core';
import { Map, LevelPoint, Ratio, ASPECT_RATIO_PRESETS } from '../../models/mapsys1';
import { EditableService } from '../../templates/EditableService';

@Injectable({
  providedIn: 'root',
})
export class MapService extends EditableService<Map> 
{
   public promptCreateNewMapPoints(areaId: string, aspectRatio: Ratio): Map 
  {
    return new Map(areaId, this.nextIndex(), aspectRatio || ASPECT_RATIO_PRESETS[0]);
  }

  constructor() 
  {
    super(Map.deepLoad, 'Map');
  }
}
