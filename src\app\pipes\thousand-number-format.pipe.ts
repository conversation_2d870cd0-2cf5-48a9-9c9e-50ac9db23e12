import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'thousandNumberFormat',
})
export class ThousandNumberFormatPipe implements PipeTransform {
  transform(value: number): string {
    if (value === undefined) {
      return '0';
    }
    let num = '';
    value
      .toString()
      .split('')
      .reverse()
      .forEach((n, index) => {
        num += index !== 0 && index !== value.toString().length - 1 && (index - 1) % 3 === 1 ? n + '.' : n;
      });
    return num.split('').reverse().join('');
  }
}
