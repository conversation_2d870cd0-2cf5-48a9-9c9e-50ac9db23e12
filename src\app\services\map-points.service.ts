import { Injectable } from '@angular/core';
import { MapPoints } from '../../models/mapsys1';
import { EditableService } from '../../templates/EditableService';

@Injectable({
  providedIn: 'root',
})
export class MapPointsService extends EditableService<MapPoints> 
{
  constructor() 
  {
    super(MapPoints.deepLoad, 'MapPoints');
  }

  public promptCreateNewMapPoints(
    id: string, name: string, classification: string, area: string, xPosition:number, yPosition:number, isShowText:boolean): MapPoints 
  {
    return new MapPoints(id, this.nextIndex(),name, classification, area, xPosition, yPosition, isShowText);
  }
}
