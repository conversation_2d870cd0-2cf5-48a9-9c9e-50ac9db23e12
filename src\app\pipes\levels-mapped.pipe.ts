import { Pipe, PipeTransform } from '@angular/core';
import { Map } from '../../models/mapsys1';

@Pipe({
  name: 'levelsMapped',
})
export class LevelsMappedPipe implements PipeTransform {
  transform(map: Map, levelIds: string[]): number {
    let levelsMapped = 0;
    levelIds.forEach((levelId) => {
      if (map.points[levelId]) {
        levelsMapped++;
      }
    });
    return levelsMapped;
  }
}
