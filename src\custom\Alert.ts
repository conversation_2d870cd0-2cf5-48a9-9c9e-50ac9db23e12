import Swal, {
  SweetAlertIcon,
  Sweet<PERSON>lertOptions,
  SweetAlertPosition,
} from 'sweetalert2';

// tslint:disable-next-line: no-namespace
export namespace Alert {

  export function inputText(title: string, text: string): Promise<any> {
    return Swal.fire({
      title,
      text,
      input: 'text',
      inputAttributes: {
        autocapitalize: 'off',
      },
      showCancelButton: true,
      confirmButtonText: 'OK',
      showLoaderOnConfirm: true,
      preConfirm: (login) => {
        return login;
      },
      allowOutsideClick: () => !Swal.isLoading(),
    }).then((result) => {
      return result.value;
    });
  }

  export function showSuccess(
    title: string,
    text?: string,
    timer?: number,
    position?: SweetAlertPosition
  ): void {
    Swal.fire({
      title,
      html: text,
      icon: 'success',
      timer,
      position,
    });
  }

  export function showError(error: any, title?: string): void {
    console.error(error.message || error);
    Alert.showAlert(title || 'Error', error.message || error, 'error');
  }

  export function showAlert(
    title: string,
    html: string,
    type: SweetAlertIcon
  ): void {
    Swal.fire(title, html, type);
  }

  export function showDropdown<T extends any>(
    title: string,
    inputOptions: { [inputValue: string]: T }
  ): Promise<T> {
    return Swal.fire({
      title,
      input: 'select',
      inputOptions,
      showConfirmButton: true,
      showCloseButton: true,
      showCancelButton: true,
      focusConfirm: false,
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
    }).then((result) => {
      if (!result) {
        return;
      }
      return result.value;
    }) as Promise<T>;
  }

  export function confirmRemove(
    ObjectName: string,
    optionalText?: string
  ): Promise<boolean> {
    return confirm(
      'Are you sure you want to remove \n"' + ObjectName + '"?',
      optionalText || '',
      'Yes, remove it!'
    );
  }

 /*  export function showChainingAlert(
    steps: Array<SweetAlertOptions | string>,
    progressSteps: string[]
  ): Promise<any> {
    return Swal.mixin({
      confirmButtonText: 'Next &rarr;',
      showCancelButton: true,
      progressSteps,
    }).queue(steps);
  }
 */
  export function confirm(title, text, confirmText, icon?): Promise<any> {
    return Swal.fire({
      title,
      text,
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: confirmText,
      icon
    }).then((result) => {
      return result.value;
    });
  }
}
