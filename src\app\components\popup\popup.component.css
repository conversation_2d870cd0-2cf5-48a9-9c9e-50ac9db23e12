.window {
  position: relative;
  box-sizing: border-box;
  flex-direction: column;
  justify-content: center;
  max-width: 100%;
  padding: 1.25em;
  border: none;
  border-radius: 0.3125em;
  background: #fff;
  font-family: inherit;
  font-size: 1rem;
}

.actions-column,
.actions-default,
.actions-row {
  display: grid;
  z-index: 1;
  flex-wrap: nowrap;
  justify-content: center;
  padding: 10px;
}
.actions-default {
  align-items: center;
}
.actions-column {
  flex-direction: column;
}
.actions-row {
  flex-direction: row;
}

.btn {
  border-width: 1px !important;
  border-color:  #fff !important;
  min-width: 200px;
}
