import { ExportableClass } from '../models/mapsys1';
import { StaticService } from './StaticService';
import { Alert } from '../custom/Alert';
import { sortData, setFilterValue } from 'src/custom/others';

export abstract class EditableService<
  T extends ExportableClass
> extends StaticService<T> {
  constructor(
    /**
     * A function that returns an empty object of type T.
     */
    deepLoad: (obj: T) => T,
    /**
     * Name of the type of object that this service manages
     * @example 'Character'
     */
    typeName: string,
    /**
     * Name of the type of object that this service manages
     * @example 'Character'
     */
    public srtLstParameter?: string
  ) {
    super(deepLoad, typeName);
  }

  public nextIndexByLocation(locationId: string): number {
    return this.data.filter((obj) => obj.id.includes(locationId + '.')).length;
  }
  protected srvAfterSave(): void {
    this.UpdateIndexes();
  }
  public UpdateIndexes(...objs: T[]): void {}

  // Data Modify Methods
  protected srvVerify(obj: T): void {}
  /**
   * Replaces an object with the same id as the modified object
   * @param obj Modified object that will replace another object with the same id
   */
  protected srvReplace(obj: T): void {
    this.data[this.indexOfId(obj.id)] = this.clone(obj);
  }
  /**
   * Removes an object from the data array
   */
  public srvRemove(objIds: string[]): T[] {
    // this.lastRemoval.next(new ListRemoval(this, id, this.indexOfId(id)));
    const removedObjs = this.filterByIds(objIds);
    this.data = this.data.filter((o) => !objIds.includes(o.id));
    return removedObjs;
  }

  /**
   * Creates an object from type T
   */
  public abstract promptCreateNewMapPoints(...args: any): Promise<T> | T;

  /**
   * Prompts an alert before removing an element from the list and returns if the object was removed
   * @todo move this method to a service level like the promptCreateNew() function from the service
   */
  public async promptRemove(obj: T): Promise<boolean> {
    const confirm = await Alert.confirmRemove(obj.id);
    if (confirm) {
      this.remove(obj.id);
      return true;
    }
    return false;
  }

  // list methods
  /**
   * Switches places of two objects from the data array
   * @param index Index of the first element to be switched
   * @param otherIndex Index of the second element to be switched
   */
  public switchPlaces(index: number, otherIndex: number): void {
    if (this.data.length <= 1) {
      return;
    }
    const obj = this.data[index];
    const otherObj = this.data[otherIndex];
    this.data[index] = otherObj;
    this.data[otherIndex] = obj;
    this.save();
  }

  /**
   * Adds an object to the data array
   * @param obj Object to be added to the data array
   * @param index Optional parameter that specified what place in the data array to add the object
   */
  protected srvAdd(obj: T, index?: number): void {
    if (index !== undefined) {
      this.data = this.data
        .slice(0, index)
        .concat(obj)
        .concat(this.data.slice(index));
      // this.switchPlaces(obj.id, this.data[index].id);
    } else {
      this.data.push(obj);
    }
  }

  /**
   * Moves an object inside the data array
   * @param obj Object that will be moved
   * @param index Index of the data array to move the object to
   */
  public move(obj: T, index: number): void {
    if (index >= this.data.length) {
      return;
    }
    this.data = this.data.filter((o) => o.id !== obj.id);
    this.data = this.data
      .slice(0, index)
      .concat(obj)
      .concat(this.data.slice(index));
    this.modify(obj);
  }

  /**
   * Adds a new object to the data array, reviews it and saves it
   */
  public add(obj: T, index?: number): T {
    if (!obj) {
      return;
    }
    try {
      if (this.findById(obj.id)) {
        throw new Error('Object with the same id already exists: ' + obj.id);
      }
      this.srvAdd(obj, index);
      this.save();
      return obj;
    } catch (error) {
      Alert.showError(error);
    }
  }

  protected srvSave(): void {
    if (this.srtLstParameter) {
      this.data = sortData(this.data, this.srtLstParameter);
    }
    this.setSRVData(this.data);
    setFilterValue(this._nextIndex, this.typeName, 'nextIndex');
  }

  /**
   * Replaces the object that has the same ID with a new object
   * @param newObj Object that will replace the other object that has the same ID.
   */
  public modify(newObj: T): void 
  {
    try 
    {
      const oldObj = this.findById(newObj.id);
      if (!oldObj) return;
      
      this.srvVerify(newObj);
      this.srvReplace(newObj);
    } 
    catch (error) 
    {
      Alert.showError(error);
    }
    this.save();
  }

  /**
   * Triggers srvUnlink, srvRemove, Unreview and then Save in order.
   */
  public remove(objIds: string | string[]): void {
    const ids: string[] = [].concat(objIds);
    const removedObjs = this.srvRemove(ids);
    if (removedObjs.length === 0) {
      return;
    }
    this.srvUnlink(removedObjs);
    this.save();
    this.lastModifiedId = undefined;
  }

  /**
   * Method triggered after removing objects that ensures that any other object
   * that points to these objects' ids have those pointers removed
   * @example srvUnlink(levels: Level[]) removes the them from the levelIds of their parent area.
   * @param objs Objects which were removed
   */
  protected srvUnlink(objs: T[]): void {}

  /**
   * sets the next index and returns it
   */
  public nextIndex(): number {
    if (this._nextIndex) {
      ++this._nextIndex;
    } else {
      this._nextIndex = this.data.length + 1;
    }
    return this._nextIndex - 1;
  }
}
