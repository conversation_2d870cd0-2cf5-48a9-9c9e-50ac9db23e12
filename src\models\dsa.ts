export class dsaVersion
{
    constructor(
        public version?:number, 
        public areaPackage?: string, 
        public levelPackage?: string, 
        public mapPointsPackage?: string,
        public itemPackage?: string, 
        public eventPackage?: string, 
        )
    {}
}

window.onload = function() 
{
    DSAVersions.availableVersions.sort((a,b) => 
    {
        return b.version - a.version;
    });
};

export class DSAVersions
{
    //IMPORTANT: The order of the parameters below MUST be the same as the constructor
    static availableVersions: dsaVersion[] = [
        new dsaVersion(6,'areaPackage','levelPackage', 'mapPointsPackage', 'itemPackage', 'eventPackage'),
        new dsaVersion(9, 'areaPkg', 'levelPkg', 'mapsPkg', 'itemPkg', 'eventPkg')//Here is the name of the package you want that is inside the DSA json file.
    ];

    static getVersion(fileFormat: string) : dsaVersion
    {    
        let ver = +fileFormat.replace('dsa', '');
        return this.availableVersions.find(x => x.version <= ver);
    }
}