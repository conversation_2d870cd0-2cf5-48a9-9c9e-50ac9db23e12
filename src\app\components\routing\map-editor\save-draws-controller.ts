import { DrawSaveModel } from './draw-save-model';
import { DrawPoint } from './draw-save-model';

export class SaveDrawsController
{
    savePointArray : DrawSaveModel = {drawPoints : [], mapName : ''}
    canSave : boolean = true;
    canGet : boolean = true;
    public firstInterval;
    public secondInterval;

    constructor(){}

    public addPointOnSavePointArray(mapName : string, point : DrawPoint )
    {   
        this.avoidCleaningLocalstorageOnInitialization(mapName);
        this.savePointArray.mapName = mapName;
        this.savePointArray.drawPoints.push(point);
        this.savePointOnLocalStorage(mapName);
    }
    
    public savePointOnLocalStorage(mapName : string) : void
    {
        this.firstInterval = setInterval(()=> 
        {
            this.canSave = true;
        }, 1000)
        if(this.canSave)
        {
            this.canSave = false;
            localStorage.setItem(mapName, JSON.stringify(this.savePointArray));
        }
    }
    public savePointsOnLocalStorage(mapName : string, points : DrawSaveModel) : void
    {
        let auxPointArray = points?.drawPoints.length > 0 ? this.savePointArray = points : this.savePointArray
        localStorage.setItem(mapName, JSON.stringify(auxPointArray));
    }
    public loadPointsFromLocalStorage(mapName : string) /* : DrawSaveModel */
    {
        this.secondInterval = setInterval(()=> 
        {
            this.canGet = true;
        }, 1000)
        if(this.canGet)
        {
            this.canGet = false;
            let drawSaveModel : DrawSaveModel = {drawPoints : [], mapName : ''};
            drawSaveModel = <DrawSaveModel>JSON.parse(localStorage.getItem(mapName)!);
            return drawSaveModel;
        } 
    }

    public deletePointsFromLocalStorage(mapName) : void
    {
        this.savePointArray.drawPoints = []
        this.savePointOnLocalStorage(mapName);
    }

    private avoidCleaningLocalstorageOnInitialization(mapName : string)
    {
        //This is to avoid cleaning the localstorage everytime the component is created.
        if(this.savePointArray.drawPoints.length <= 0)
        {/* 
            let localStorageArray = this.loadPointsFromLocalStorage(mapName);
            this.savePointArray = localStorageArray?.drawPoints?.length > 0 ? localStorageArray : this.savePointArray; */
        }
    }
}