import { EditorUtilities } from './EditorUtilities';
import { <PERSON>vas<PERSON>enderer } from './CanvasRenderer';
import { Component, OnDestroy, OnInit, Output } from '@angular/core';
import { MapService } from '../../../services/map.service';
import { PopupService } from '../../../services/popup.service';
import { UserSettingsService } from '../../../services/user-settings.service';
import { AreaService } from '../../../services/area.service';
import { Ratio, Layout, Map, LevelPoint, Area, Level, PixelDimension, ItemEvent} from '../../../../models/mapsys1';
import { Popup } from '../../../../custom/Popup';
import { createDivBox } from '../../../../custom/DivStyles';
import { Router } from '@angular/router';
import { LevelService } from '../../../../app/services/level.service';
import { highlightElement } from '../../../../custom/others';
import { Alert } from '../../../../custom/Alert';
import { AppService } from '../../../../app/services/app.service';
import { EditorService } from '../../../../app/services/editor.service';
import { Canvas } from './Canvas';
import { MapPointsService } from '../../../../app/services/map-points.service';
import { DataService } from '../../../../app/services/data.service';
import { DrawHermite } from './draw-hermite';
import { DrawOnCanvas } from './draw-on-canvas';
import { DrawItems } from './draw-items';
import { SelectItem } from './select-item';
import { ModalService } from '../../../../app/services/modal.service';
import { DrawBackgroundImagesService } from '../../../../app/services/draw-background-images.service';
import { ImageRedimensionService } from '../../../../app/services/image-redimension.service';
import { DevicesService } from '../../../../app/services/devices.service';
import { DrawTextImage } from './draw-text-image';


type ExpansionMode = 'expand-canvas' | 'shrink-canvas';

export interface Position 
{
  x: number;
  y: number;
}

export interface Devices 
{
  deviceType:string
  deviceName:string,
  deviceSize:number,
  devicePPI:number,
  deviceResolution:string
}

@Component({
  selector: 'app-map-editor',
  templateUrl: './map-editor.component.html',
  styleUrls: ['./map-editor.component.scss'],
})


export class MapEditorComponent implements OnInit, OnDestroy
{
  drawTextImage : DrawTextImage;
  state = 'map';
  canControlModal : boolean = false;
  public editorWrapper: HTMLElement;
  public interactionLayer: HTMLElement;
  public viewPort: Canvas;

  // Canvas único que substitui todos os canvas anteriores
  public unifiedCanvas: Canvas;

  // Propriedades para compatibilidade com código existente
  public get editorPort(): Canvas { return this.unifiedCanvas; }
  public get mapCanvas(): Canvas { return this.unifiedCanvas; }
  public get drawCanvas(): Canvas { return this.unifiedCanvas; }
  public get itemCanvas(): Canvas { return this.unifiedCanvas; }
  isMouseDoublePressed = false;
  public highlightLevelsInEditor: boolean;
  public canvasRenderer: CanvasRenderer;
  _drawHermite : DrawHermite;
  public _drawOnCanvas : DrawOnCanvas;
  public _drawItems : DrawItems;
  public _selectItem : SelectItem;
  public itemEvent : ItemEvent[] = [];
  zooming: boolean;
  intervalNum: any;
  secondIntervalNum: any;
  expansionMode: ExpansionMode = 'expand-canvas';
  levelHeights: { [levelId: string]: number } = {};
  selectedLevelId: string | undefined;
  detectedTextPointId: string | undefined; // Para detectar textPoints sob o cursor
  private _viewPortOffsetPosition = { x: 0, y: 0 };
  private _mousePosViewPort: { x: number; y: number };
  public map: Map;
  public area: Area;
  public levelsFromArea: Level[];
  maxLevelHeight = 0;
  newLevelIds: string[] = [];
  detectedLevelId: string;
  textPoints: any[] = [];
  @Output() currentSelectedColor: string = '#0400FF';
  currentHermiteCurveThickness: number = 1;
  currentHermiteCurveTension: number = 0.5;
  currentHermiteCurveColor: string = 'blue';
  currentCurveThickness : number = 5;
  mapsIds;
  isMousePressed = false;
  isShowImage = false;
  isShowText = false;
  isShowBackground = false;
  isShowControlPoints = false;
  isShowCanDraw = true;
  isShowItems = false;
  canDeleteDrawSection : boolean = false;
  thridTimeout;
  fourthTimeout;
  editorUtilities;
  element;
  oldElement;
  point;
  mouseX;
  mouseY;
  x = 1;
  y = 10;
  devices: Devices[] = [];


  constructor(
    private _mapService: MapService,
    private _areaService: AreaService,
    private _popupService: PopupService,
    private _levelService: LevelService,
    private _appService: AppService,
    private _router: Router,
    private _editorService: EditorService,
    public _mapPointsService: MapPointsService,
    public _dataService: DataService,
    public _modalService : ModalService,
    private _userSettingsService: UserSettingsService,
    private _drawBackgroundImagesService : DrawBackgroundImagesService,
    private _imageRedimensionService: ImageRedimensionService,
    private _devicesService: DevicesService
  ) {}

  public get zoomValue(): number 
  {
    let zoom = this._userSettingsService.getEditorConfigurations(this.map.id).zoomValue;
    return zoom;
  }

  public set zoomValue(value: number) 
  {
    this._userSettingsService.setEditorConfigurations(this.map.id, 'zoomValue', value);
  }

  private get _viewPortPivot(): { x: number; y: number } 
  {
    return this._userSettingsService.getEditorConfigurations(this.map.id).viewPortPivot;
  }

  private set _viewPortPivot(pos: { x: number; y: number }) 
  {
    this._userSettingsService.setEditorConfigurations(this.map.id, 'viewPortPivot', pos);
  }


  get mapLayout(): Layout 
  {
    return this.map.aspectRatio.a > this.map.aspectRatio.c ? 'vertical' : 'horizontal';
  }

  offsetBoundaries(canvasDimension: PixelDimension, frame: PixelDimension): { maxX: number; minX: number; maxY: number; minY: number } 
  {
    return {
      maxX: canvasDimension.width > frame.width ? -(canvasDimension.width - frame.width) : 0,
      minX: 0,
      maxY: canvasDimension.height > frame.height ? -(canvasDimension.height - frame.height) : 0,
      minY: 0,
    };
  }

  ngOnInit(): void
  {
    this.devices = this._devicesService.devices;
    // checks if the map exists
    this.map = this._mapService.findById(this._mapService.lastModifiedId);

    if (!this.map)
    {
      this._router.navigate(['manager']);
      return;
    }

    // assigns basic reference data
    this.area = this._areaService.findById(this.map.areaId);
    this.levelsFromArea = this._levelService.filterByIds(this.area.levelIds);
    this.generateLevelHeights();
    this.highlightLevelsInEditor = this._userSettingsService.data.highlightLevelsInEditor;

    this.levelsFromArea.forEach((level) =>
    {
      const levelPoint = this.map.points[level.id];
      if (!levelPoint)
      {
        this.newLevelIds.push(level.id);
        return;
      }
    });

    this.newLevelIds.forEach((levelId) =>
    {
      this._editorService.createMapPoint(this.map, levelId);
    });

   this._editorService.clampMap(this.map);

    this._dataService._mapPointsService.data.forEach((t) =>
    {
      if (t.area?.split(':')[1]?.split(' ').join('').trim() === this.area.name?.split(' ').join(''))
      {
        this.textPoints.push(t);
      }
    });

    // === LIMPEZA DE CONEXÕES DOS NÍVEIS PRINCIPAIS ===
    // Remove qualquer referência a textPoints dos linkedLevelIds dos níveis principais
    this.cleanupMainLevelConnections();

    // === VERIFICAÇÃO CRÍTICA ===
    // Verifica se algum textPoint foi incluído incorretamente no levelsFromArea
    this.verifyDataIntegrity();

    this.generateCirclesPositions();
    this.initializeCanvas();
    this.initializeHermiteCurves();
    this.gettingScreenPPI();
    this.calculateScreenZoomPercentage();
    console.log('Conteúdo do array textPoints:',this.textPoints);
    console.log('Conteúdo do array levelsFromArea:',this.levelsFromArea);
  }

  /**
   * Remove qualquer referência a textPoints dos linkedLevelIds dos níveis principais
   * Garante que círculos vermelhos/azuis nunca sejam incluídos nas conexões
   */
  private cleanupMainLevelConnections(): void {
    if (!this.textPoints || this.textPoints.length === 0) return;

    // Cria conjunto de IDs de textPoints para verificação rápida
    const textPointIds = new Set(this.textPoints.map(tp => tp.id));

    // Verifica e limpa os linkedLevelIds de todos os níveis principais
    this.levelsFromArea.forEach(level => {
      if (level.linkedLevelIds && level.linkedLevelIds.length > 0) {
        const originalLength = level.linkedLevelIds.length;

        // Remove qualquer ID que seja de um textPoint
        level.linkedLevelIds = level.linkedLevelIds.filter(linkedId => !textPointIds.has(linkedId));

        if (level.linkedLevelIds.length !== originalLength) {
          console.log(`🔗 Removendo ${originalLength - level.linkedLevelIds.length} conexões inválidas do nível ${level.id}`);
        }
      }
    });

    console.log('✅ Limpeza de conexões concluída');
  }

  /**
   * Verifica a integridade dos dados para garantir separação entre níveis e textPoints
   */
  private verifyDataIntegrity(): void {
    console.log('🔍 Verificando integridade dos dados...');

    // Cria conjuntos de IDs para verificação cruzada
    const textPointIds = new Set(this.textPoints.map(tp => tp.id));
    const levelIds = new Set(this.levelsFromArea.map(level => level.id));

    // Verifica se há sobreposição entre textPoints e levels
    const overlap = [...textPointIds].filter(id => levelIds.has(id));
    if (overlap.length > 0) {
      console.error('❌ PROBLEMA CRÍTICO: TextPoints incluídos em levelsFromArea:', overlap);

      // Remove textPoints do array levelsFromArea
      this.levelsFromArea = this.levelsFromArea.filter(level => !textPointIds.has(level.id));
      console.log('🔧 Removidos textPoints do levelsFromArea');
    }

    // Verifica se textPoints têm linkedLevelIds (não deveriam ter)
    this.textPoints.forEach(tp => {
      if ((tp as any).linkedLevelIds && (tp as any).linkedLevelIds.length > 0) {
        console.warn(`⚠️ TextPoint ${tp.id} tem linkedLevelIds:`, (tp as any).linkedLevelIds);
      }
    });

    // Log final dos dados limpos
    console.log('📊 Dados após verificação:', {
      textPointsCount: this.textPoints.length,
      levelsCount: this.levelsFromArea.length,
      textPointIds: [...textPointIds],
      levelIds: [...levelIds]
    });
  }

  ppi: number;

  // Performance optimization properties
  private animationFrameId: number | null = null;
  private pendingUpdate = false;
  private lastUpdateTime = 0;
  private readonly UPDATE_THROTTLE_MS = 16; // ~60fps

  gettingScreenPPI()
  {
    //width of the screen in CSS pixels
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    // interior width of the window in pixels
    const screenPixelWidth = window.innerWidth;
    const screenPixelHeight = window.innerHeight;

    const screenInchesWidth = screenWidth / window.devicePixelRatio;
    const screenInchesHeight = screenHeight / window.devicePixelRatio;

    const horizontalPpi = screenPixelWidth / screenInchesWidth;
    const verticalPpi = screenPixelHeight / screenInchesHeight;

    let pixelDiaganol = Math.sqrt(Math.pow(screenWidth, 2) + Math.pow(screenHeight, 2));
    let inchDiaganol = Math.sqrt(Math.pow(horizontalPpi, 2) + Math.pow(verticalPpi, 2));

    this.ppi = pixelDiaganol / inchDiaganol;
  }


  initializeHermiteCurves()
  {
    this.currentHermiteCurveThickness = this._userSettingsService.getHermiteCurveThickness();
    this._drawHermite.changeThickness(this.currentHermiteCurveThickness);
    
    this.currentHermiteCurveTension = this._userSettingsService.getHermiteCurveTension();
    this._drawHermite.changeTension(this.currentHermiteCurveTension);
    
    this.currentHermiteCurveColor = this._userSettingsService.getHermiteCurveColor();
    this._drawHermite.changeColor(this.currentHermiteCurveColor);

    this.currentSelectedColor = this._userSettingsService.getCurrentDrawColor();
    this._drawOnCanvas.setCurveColor(this.currentSelectedColor);

    this.currentCurveThickness = this._userSettingsService.getCurrentDrawThickness();
    this._drawOnCanvas.setCurveThickness(this.currentCurveThickness);
  }

  controlModalWindow(modalWindowStatus : any)
  {
    this.canControlModal = modalWindowStatus;
  }

  private generateLevelHeights(): void 
  {
    this.levelsFromArea.forEach((level, levelIndex) => 
    {
      this.assignLevelHeight(level, levelIndex);
    });
  }

  public toggleHighlightLevelsInEditor(): void 
  {
    this.highlightLevelsInEditor = !this.highlightLevelsInEditor;

    this._userSettingsService.setHighlightLevelsInEditor(
      this.highlightLevelsInEditor
    );
  }

  private assignLevelHeight(level: Level, levelIndex: number): void 
  {
    this.levelHeights[level.id] = -1;
    this.levelsFromArea.forEach((otherLevel, parentIndex) => {
      const parent = otherLevel.linkedLevelIds.includes(level.id)
        ? otherLevel
        : undefined;
      if (!parent || parentIndex > levelIndex) return;

      const heightOfParent = this.levelHeights[parent.id];
      let lIndex =
        parent.linkedLevelIds.indexOf(level.id) -
        parent.linkedLevelIds.filter(
          (linkedLevelId) => this.area.id !== Area.getSubIdFrom(linkedLevelId)
        ).length;

      lIndex = lIndex < 0 ? 0 : lIndex;

      if (
        this.levelHeights[level.id] === -1 ||
        heightOfParent + lIndex < this.levelHeights[level.id]
      ) 
      {
        this.levelHeights[level.id] = heightOfParent + lIndex;
      }
    });

    if (this.levelHeights[level.id] === -1) 
    {
      this.levelHeights[level.id] = 0;
    }
    this.maxLevelHeight = this.levelHeights[level.id] > this.maxLevelHeight
        ? this.levelHeights[level.id] : this.maxLevelHeight;
  }


  /**
   * Inicializa o canvas único e a camada de interação
   * Substitui a inicialização de múltiplos canvas por um sistema unificado
   */
  initializeCanvas()
  {
    // Obtém referências aos elementos DOM
    this.editorWrapper = document.getElementById('editor-wrapper') as HTMLElement;
    this.interactionLayer = document.getElementById('interaction-layer') as HTMLElement;

    // Cria o canvas único que substitui todos os canvas anteriores
    const canvasElement = document.getElementById('unified-canvas') as HTMLCanvasElement;

    if (!canvasElement) {
      console.error('❌ Canvas element não encontrado!');
      return;
    }

    this.unifiedCanvas = new Canvas(
      canvasElement,
      this._userSettingsService.data.deadZoneSize,
      this._userSettingsService.data.levelDiameter,
      this.map?.aspectRatio
    );

    // Inicializa o viewport (mantém separado pois é usado para preview)
    this.viewPort = new Canvas(
      document.getElementById('view-port') as HTMLCanvasElement,
      this._userSettingsService.data.deadZoneSize,
      this._userSettingsService.data.levelDiameter,
      this._userSettingsService.data?.aspectRatios[0]
    );

    // Cria o renderer adaptado para o canvas único
    this.canvasRenderer = new CanvasRenderer(
      this.unifiedCanvas, // drawCanvas
      this.unifiedCanvas, // itemCanvas
      this._areaService,
      this.unifiedCanvas, // mapCanvas
      this.viewPort,
      this.unifiedCanvas, // editorPort
      this._userSettingsService,
      this._drawBackgroundImagesService
    );

    // Inicializa os componentes de desenho e interação
    this._drawHermite = new DrawHermite(this.unifiedCanvas, this.map, this.levelsFromArea);
    this._drawOnCanvas = new DrawOnCanvas(this.area.name, this.unifiedCanvas);
    this._drawItems = new DrawItems(this.unifiedCanvas, this._mapService);
    this._selectItem = new SelectItem();

    // Configura dimensões e atualiza o canvas
    this.redimensionViewPort();
    this.redimensionMapCanvas();
    this.setupInteractionLayer();

    // Força a criação inicial do canvas com dimensões visíveis
    this.forceCanvasInitialization();

    this.canDraw();
    this.updateMapCanvas();
    this.initializeHermiteCurves();
  }

  /**
   * Força a inicialização do canvas com dimensões corretas baseadas no aspect ratio
   *
   * Este método garante que o canvas seja inicializado com as dimensões adequadas
   * desde o primeiro carregamento, evitando que apareça com tamanho incorreto
   * até que o usuário acione o zoom.
   *
   * Funcionalidades:
   * - Calcula dimensões baseadas no aspect ratio do mapa
   * - Aplica redimensionamento usando a mesma lógica do zoom
   * - Força renderização imediata do conteúdo
   * - Fornece logs detalhados para debug
   *
   * É chamado durante a inicialização do componente para garantir
   * que o canvas apareça corretamente desde o início.
   */
  private forceCanvasInitialization(): void
  {
    if (!this.unifiedCanvas?.el) {
      console.error('❌ Canvas element não disponível para forceCanvasInitialization');
      return;
    }

    // Usa o mesmo método que o zoom usa para calcular dimensões
    // Isso garante consistência entre inicialização e zoom
    this.redimensionMapCanvas();

    // Força a renderização do mapa com as dimensões corretas
    this.renderMapContent();
  }

  /**
   * Renderiza o conteúdo do mapa no canvas único
   *
   * Este método é responsável por coordenar a renderização completa do mapa
   * usando o CanvasRenderer. Ele serve como ponte entre o componente e o
   * sistema de renderização, garantindo que todos os dados necessários
   * estejam disponíveis antes de iniciar o desenho.
   *
   * Funcionalidades:
   * - Validação de dados necessários (mapa, área, renderer)
   * - Limpeza do canvas antes da renderização
   * - Chamada do CanvasRenderer com todos os parâmetros
   * - Logs detalhados para debug e monitoramento
   * - Tratamento de erros durante renderização
   *
   * Dados passados para renderização:
   * - Mapa e área atuais
   * - Níveis da área
   * - IDs de níveis detectados e novos
   * - Text points e configurações de exibição
   */
  private renderMapContent(): void
  {
    // Validação de dependências críticas
    if (!this.canvasRenderer || !this.map || !this.area) {
      console.log('⚠️ Dados necessários não disponíveis para renderização:', {
        hasRenderer: !!this.canvasRenderer,
        hasMap: !!this.map,
        hasArea: !!this.area,
        hasLevels: this.levelsFromArea?.length || 0,
        mapPoints: this.map ? Object.keys(this.map.points).length : 0,
        areaLevelIds: this.area ? this.area.levelIds?.length : 0
      });
      return;
    }

    try {
      // Log detalhado dos dados que serão renderizados
      console.log('🎨 Dados para renderização:', {
        mapId: this.map.id,
        areaId: this.area.id,
        levelsCount: this.levelsFromArea?.length || 0,
        mapPointsCount: Object.keys(this.map.points).length,
        areaLevelIdsCount: this.area.levelIds?.length || 0,
        detectedLevelId: this.detectedLevelId,
        newLevelIds: this.newLevelIds,
        textPointsCount: this.textPoints?.length || 0
      });

      // Limpa todo o canvas antes de renderizar
      // Isso garante que não haja restos de renderizações anteriores
      this.unifiedCanvas.context.clearRect(0, 0, this.unifiedCanvas.el.width, this.unifiedCanvas.el.height);

      // Chama o CanvasRenderer para desenhar todo o conteúdo do mapa
      // Passa todos os dados necessários incluindo estados visuais
      this.canvasRenderer.drawMapCanvas(
        this.map,                    // Mapa com posições dos níveis
        this.area,                   // Área atual
        this.levelsFromArea,         // Níveis da área
        this.detectedLevelId,        // Nível sob o cursor (hover)
        this.newLevelIds,            // Níveis recém criados
        this.textPoints,             // Pontos de texto/imagem
        this.isShowText,             // Flag para mostrar text points
        this.isShowImage,            // Flag para mostrar image points
        this.detectedTextPointId     // ✅ NOVO: TextPoint sob o cursor (hover)
      );

    } catch (error) {
      console.error('❌ Erro ao renderizar mapa:', error);
    }
  }

  /**
   * Configura a camada de interação para capturar eventos do mouse
   * Sincroniza as dimensões com o canvas principal de forma dinâmica
   */
  private setupInteractionLayer(): void
  {
    // Com o wrapper CSS, a div de interação está automaticamente alinhada
    // Não é necessária configuração manual de dimensões ou posicionamento
    if (!this.interactionLayer || !this.unifiedCanvas) {
      return;
    }
  }

  /**
   * Remove todos os desenhos salvos no localStorage e atualiza o canvas
   *
   * Funcionalidades:
   * - Solicita confirmação do usuário antes de apagar
   * - Limpa os dados de desenho do localStorage e do array interno
   * - Desativa o modo de exclusão de desenho
   * - Força atualização do canvas para refletir a remoção
   *
   * Comportamento correto:
   * - Remove apenas os desenhos da funcionalidade de desenho
   * - Mantém todos os outros elementos do mapa (círculos, deadzone, etc.)
   * - Atualiza o canvas para mostrar o mapa sem os desenhos
   */
  async deleteDraw()
  {
    let response : boolean = await Alert.confirmRemove('ALL draws', 'This action cannot be undo!');
    if(response)
    {
      this.canDeleteDrawSection = false;

      // === LIMPEZA CORRETA DOS DESENHOS ===
      // Remove os dados de desenho do localStorage (nova chave com '_strokes')
      localStorage.removeItem(this.area.name + '_strokes');

      // Remove também a chave antiga para compatibilidade
      localStorage.removeItem(this.area.name);

      // === LIMPEZA DO ARRAY INTERNO ===
      // Usa o método clearAllCanvas() que limpa o array interno e salva
      this._drawOnCanvas.clearAllCanvas();

      // === ATUALIZAÇÃO DO CANVAS ===
      // Força redesenho completo do mapa sem os desenhos
      this.updateMapCanvas();
    }
  }

  deleteDrawSegment()
  {
    this.canDeleteDrawSection = !this.canDeleteDrawSection;
  }

  // button clicks
  toggleExpansionMode(): void 
  {
    this.expansionMode = this.expansionMode === 'expand-canvas' ? 'shrink-canvas' : 'expand-canvas';
  }

  resizeMap(direction: 'width' | 'height'): void 
  {
    const newAspectRatio = new Ratio(this.map.aspectRatio.a, this.map.aspectRatio.c);

    if (direction === 'height') 
    {
      if (newAspectRatio.a > newAspectRatio.c) 
      {
        newAspectRatio.a = newAspectRatio.a + 0.5;
      } 
      else 
      {
        newAspectRatio.c = newAspectRatio.c - 0.5;
      }
    } 
    else 
    {
      if (newAspectRatio.a < newAspectRatio.c)
      {
        newAspectRatio.c = newAspectRatio.c + 0.5;
      }
      else 
      {
        newAspectRatio.a = newAspectRatio.a - 0.5;
      }
    }

    if (newAspectRatio.a < 1) 
    {
      newAspectRatio.c += 1 - newAspectRatio.a;
      newAspectRatio.a = 1;
    } 
    else if (newAspectRatio.c < 1) 
    {
      newAspectRatio.a += 1 - newAspectRatio.c;
      newAspectRatio.c = 1;
    }

    this._editorService.setMapAspectRatio(this.map, newAspectRatio);
    this.map.aspectRatio = newAspectRatio;
    this.unifiedCanvas.ratio = newAspectRatio;
    this.redimensionMapCanvas();
    this.updateMapCanvas();
  }


  startZooming(i : number): void 
  {
    this.intervalNum = setTimeout(() => 
    {
      this.zoom(i);
    }, 20);
  }
  
  private zoom(i: number): void
  {
    if (this.zoomValue + i > 100 || this.zoomValue + i < 10) return;
    this.zoomValue += i;
    this.calculateImageZoomPercentage();
    this.redimensionMapCanvas();
    this.updateMapCanvas();
  }

  detectPoint(levelId: string): void 
  {
    // scroll to canvas point by level width thing
    const point = this.map.points[levelId];
    if (!point) 
    {
      this.detectedLevelId = undefined;
      return;
    }
    this.detectedLevelId = levelId;

    const positionInPixels = this.editorPort.positionInPixels(point.position);

    this.editorWrapper.scrollLeft = positionInPixels.x;
    this.editorWrapper.scrollTop = positionInPixels.y;
    //this.updateMapCanvas();
  }

  createPoint(levelId: string): void 
  {
    if (this.map.points[levelId]) 
    {
      this.detectPoint(levelId);
      return;
    }

    this._editorService.createMapPoint(
      this.map,
      levelId,
      new LevelPoint({ xd: 50, yd: 50 })
    );
    this.detectPoint(levelId);

    this.assignLevelHeight(
      this.levelsFromArea.find((x) => x.id == levelId),
      this.levelsFromArea.findIndex((x) => x.id == levelId)
    );
    this.updateMapCanvas();
  }

  async removePoint(levelId): Promise<void> 
  {
    if (!(await Alert.confirmRemove(levelId))) return;

    this._editorService.removeMapPoint(this.map, levelId);
    this.updateMapCanvas();
  }

  // redimensions canvas elements
  /**
   * Redimensiona o canvas único respeitando o aspect ratio definido
   * Suporte completo para mapas de qualquer altura sem limitações artificiais
   * Otimizado para performance com mapas grandes
   */
  redimensionMapCanvas(): void
  {
    // Verifica se os objetos necessários estão disponíveis
    if (!this.unifiedCanvas) {
      console.error('❌ unifiedCanvas não disponível');
      return;
    }

    // === CÁLCULO DINÂMICO DE DIMENSÕES BASE ===
    // Remove limitações artificiais e calcula baseado no aspect ratio real
    const aspectRatio = this.unifiedCanvas.ratio;
    let baseWidth: number;
    let baseHeight: number;

    // Calcula dimensões base inteligentes baseadas no aspect ratio
    if (aspectRatio.c >= aspectRatio.a) {
      // Mapa mais largo que alto (landscape)
      baseWidth = Math.max(800, aspectRatio.c * 200); // Escala dinâmica
      baseHeight = (baseWidth * aspectRatio.a) / aspectRatio.c;
    } else {
      // Mapa mais alto que largo (portrait)
      baseHeight = Math.max(600, aspectRatio.a * 200); // Escala dinâmica
      baseWidth = (baseHeight * aspectRatio.c) / aspectRatio.a;
    }

    if (this.map?.aspectRatio) {
      if (this.editorWrapper?.offsetWidth > 0) {
        // Usa a largura do wrapper se disponível
        const wrapperWidth = this.editorWrapper.offsetWidth;
        const mapDimension = this.map.aspectRatio.toDimension({ width: wrapperWidth });
        baseWidth = mapDimension.width;
        baseHeight = mapDimension.height;
 
      } else {
        // Usa dimensões baseadas no aspect ratio com largura padrão
        const mapDimension = this.map.aspectRatio.toDimension({ width: 900 });
        baseWidth = mapDimension.width;
        baseHeight = mapDimension.height;    
      }
    } else {
      console.log('🔧 Dimensões base (fallback):', { baseWidth, baseHeight });
    }

    // Aplica o zoom às dimensões base
    const zoomFactor = (this.zoomValue || 100) * 0.01;
    const targetWidth = Math.floor(baseWidth * zoomFactor);
    const targetHeight = Math.floor(baseHeight * zoomFactor);

    // Redimensiona o canvas único
    this.unifiedCanvas.redimension(new PixelDimension(targetWidth, targetHeight), true);

   // Atualiza o canvas temporário de desenho para corresponder às novas dimensões
    if (this._drawOnCanvas) {
      this._drawOnCanvas.updateDrawOnlyCanvasDimensions();
    }

    // Atualiza a camada de interação para manter sincronização
    requestAnimationFrame(() => {
      this.setupInteractionLayer();
    });
  }

  redimensionViewPort(): void 
  {
    this.viewPort.redimension(new PixelDimension(300, 300), false, 'vertical');
  }

  // change canvas ratios
  private async selectRatio(ratios: Ratio[]): Promise<Ratio> {
    const selectedRatioButton = await this._popupService.fire<Ratio, Ratio>(
      new Popup.Interface(
        {
          title: 'Change the Aspect Ratio',
          actionsClass: 'column',
        },
        this._userSettingsService.ratioButtons(ratios),
        {
          inputButton: { value: ratios[0] },
          next: (button) => {
            if (!button) return;

            return new Popup.Interface<Ratio, Layout>(
              {
                title: 'Select Layout',
                actionsClass: 'column',
              },
              [
                new Popup.Button(
                  button.value.a > button.value.c ? 'Vertical' : 'Horizontal',
                  button.value,
                  {
                    divStyle: createDivBox(
                      100,
                      button.value.a * (100 / button.value.c),
                      'grey',
                      'dotted'
                    ),
                    divText: button.value.c + 'x' + button.value.a,
                  }
                ),

                new Popup.Button(
                  button.value.c > button.value.a ? 'Vertical' : 'Horizontal',
                  button.value.invert(),
                  {
                    divStyle: createDivBox(
                      100,
                      button.value.c * (100 / button.value.a),
                      'grey',
                      'dotted'
                    ),
                    divText: button.value.a + 'x' + button.value.c,
                  }
                ),
              ]
            );
          },
        }
      )
    );
    if (!selectedRatioButton) return;

    return selectedRatioButton.value;
  }

  async changeViewPortRatio(): Promise<void> {
    const selectedRatio = await this.selectRatio(
      this._userSettingsService.data.aspectRatios
    );
    if (!selectedRatio) return;

    this._userSettingsService.setViewPortRatio(selectedRatio);
    this.viewPort.ratio = selectedRatio;

    this._viewPortOffsetPosition.x = -this._viewPortPivot.x;
    this._viewPortOffsetPosition.y = -this._viewPortPivot.y;

    this.redimensionViewPort();
    this.updateMapCanvas();
    this.canvasRenderer.updateViewPort(
      this._viewPortPivot,
      this._viewPortOffsetPosition
    );
  }

  // ========== NOVOS MÉTODOS DE EVENTOS DO MOUSE PARA CANVAS ÚNICO ==========

  /**
   * Manipula eventos de mouse down no canvas único
   *
   * Este método centraliza toda a lógica de início de interações no mapa.
   * É o ponto de entrada para todas as ações que começam com um clique,
   * incluindo seleção de níveis, início de arrasto, desenho e seleção de itens.
   *
   * Funcionalidades principais:
   * - Detecção e seleção de níveis sob o cursor
   * - Configuração de desenho livre no canvas
   * - Seleção de itens especiais (se habilitado)
   * - Movimento de texto/imagem
   * - Atualização do cursor baseada no contexto
   * - Integração com sistema de destaque no editor
   *
   * Fluxo de execução:
   * 1. Captura posição do mouse
   * 2. Configura desenho se necessário
   * 3. Detecta nível sob o cursor
   * 4. Aplica destaque no editor
   * 5. Processa seleção de itens
   * 6. Inicia movimento de elementos
   * 7. Atualiza cursor
   */
  handleMouseDown(event: MouseEvent): void
  {
    this.isMousePressed = true;
    const mousePos = this.getMousePosFromInteractionLayer(event);

    // === CONFIGURAÇÃO DE DESENHO LIVRE ===
    // Prepara posição inicial para desenho se o modo estiver ativo
    if (this.isShowCanDraw) {
      this._drawOnCanvas.oldMousePosition = mousePos;
    }

    // Evita processamento duplo em cliques duplos
    if (this.isMouseDoublePressed) return;

    // REMOVIDO: updateMapCanvas() que estava limpando o canvas incorretamente
    // A limpeza agora é feita apenas quando necessário durante a renderização

    // === DETECÇÃO E SELEÇÃO DE NÍVEIS ===
    // Só permite seleção de níveis se o modo de desenho estiver desativado
    // Quando isShowCanDraw está ativo, o foco deve ser apenas no desenho
    if (!this.isShowCanDraw) {
      // Identifica qual nível (se algum) está sob o cursor
      this.selectedLevelId = this.detectLevel(event, true); 
    }

    // === DESTAQUE NO EDITOR ===
    // Aplica destaque no painel lateral se configurado
    if (this._userSettingsService.data.highlightLevelsInEditor && this.selectedLevelId) {
      highlightElement(this.selectedLevelId, 1, true, undefined);
    }

    // === SELEÇÃO DE ITENS ESPECIAIS ===
    // Processa seleção de itens se o modo estiver ativo
    if (this.isShowItems) {
      this._modalService.itemEvent = this._selectItem.selectItem(
        this._drawItems.itemEvent,
        mousePos,
        this.unifiedCanvas
      );

      // Controla exibição de modal baseado na seleção
      if (this._modalService.itemEvent.length > 0) {
        this._modalService.canControlModal = !this._modalService.canControlModal;
      } else {
        this._modalService.canControlModal = false;
      }
    }

    // === MOVIMENTO DE TEXTO/IMAGEM ===
    // Inicia movimento de elementos de texto ou imagem
    this.moveTextImagePosition(event);

    // === ATUALIZAÇÃO DO CURSOR ===
    // Atualiza cursor baseado no contexto atual (desenho, hover, etc.)
    this.updateCursorForDrawMode();
  }

  /**
   * Manipula eventos de mouse move no canvas único
   * Centraliza toda a lógica de arrasto e movimento
   */
  handleMouseMove(event: MouseEvent): void
  {
    if (!this.isMousePressed) {
      this.detectLevel(event, false);
      // ✅ NOVA FUNCIONALIDADE: Detecta textPoints quando isShowText está ativo
      if (this.isShowText || this.isShowImage) {
        this.detectTextPoint(event, false);
      }
      return;
    }

    const mousePos = this.getMousePosFromInteractionLayer(event);

    // Lógica de desenho no canvas
    this.drawOnCanvas(mousePos);

    // Movimento de texto/imagem
    this.handleMouseMoveTextImage(mousePos);
      this.dragPointLogic(mousePos);

  }

  /**
   * Manipula eventos de mouse up no canvas único
   * Finaliza todas as interações
   */
  handleMouseUp(event?: MouseEvent): void
  {
    this.isMousePressed = false;
    this.point = undefined;
    this.selectedLevelId = undefined;

    // Salva desenhos se necessário
    if (this.isShowCanDraw || this.canDeleteDrawSection) {
      this._drawOnCanvas.saveDrawOnLocalStorage();
    }

    // Atualiza cursor
    this.updateCursorForDrawMode();

    // Lógica do viewport
    if (this._mousePosViewPort) {
      this._viewPortPivot = {
        x: (this._viewPortPivot.x += this._viewPortOffsetPosition.x),
        y: this._viewPortPivot.y + this._viewPortOffsetPosition.y,
      };
      this._viewPortOffsetPosition.x = 0;
      this._viewPortOffsetPosition.y = 0;
      this._mousePosViewPort = undefined;
    }
  }

  /**
   * Manipula eventos de mouse leave no canvas único
   */
  handleMouseLeave(event: MouseEvent): void
  {
    // Chama a lógica de movimento para finalizar interações
    this.handleMouseMove(event);
  }

  // ========== MÉTODOS DE COMPATIBILIDADE ==========

  /**
   * @deprecated Método mantido para compatibilidade - usa handleMouseUp
   */
  releaseMouse(): void {
    this.handleMouseUp();
  }

  // ========== MÉTODOS AUXILIARES PARA CANVAS ÚNICO ==========

  /**
   * Atualiza o cursor baseado no modo de desenho atual
   */
  private updateCursorForDrawMode(): void
  {
    if (!this.interactionLayer) return;

    if (this.isShowCanDraw && !this.canDeleteDrawSection) {
      this.interactionLayer.style.cursor = "cell";
    } else if (this.canDeleteDrawSection) {
      this.interactionLayer.style.cursor = "crosshair";
    } else {
      this.interactionLayer.style.cursor = "default";
    }
  }

  /**
   * Manipula movimento do mouse para texto/imagem
   */
  /**
   * Manipula o movimento dos círculos vermelhos (text) e azuis (image)
   * Garante que eles não se conectem com linhas tracejadas
   */
  private handleMouseMoveTextImage(mousePos: { x: number; y: number }): void
  {
    if (!this.point) return;

    this.textPoints.forEach((a) => {
      if (a.id === this.point?.id) {
        // === ATUALIZAÇÃO OTIMIZADA PARA MOVIMENTO DE TEXTO ===
        // Usa método otimizado com throttling para melhor performance
        this.updateMapCanvasOptimized();

        // Making the point position become percentage to be saved on the database
        let mouseX = ((mousePos.x - this.unifiedCanvas.deadZoneInPixels) / this.unifiedCanvas.dimension.width) * 100;
        let mouseY = ((mousePos.y - this.unifiedCanvas.deadZoneInPixels) / this.unifiedCanvas.dimension.height) * 100;

        // Verifica se o mouse está dentro dos limites do mapa
        if (
          mousePos.x >= this.unifiedCanvas.deadZoneInPixels + this.unifiedCanvas.circleRadiusInPixels &&
          mousePos.x <= this.unifiedCanvas.deadZoneInPixels + this.unifiedCanvas.dimension.width - this.unifiedCanvas.circleRadiusInPixels &&
          mousePos.y >= this.unifiedCanvas.deadZoneInPixels + this.unifiedCanvas.circleRadiusInPixels &&
          mousePos.y <= this.unifiedCanvas.deadZoneInPixels + this.unifiedCanvas.dimension.height - this.unifiedCanvas.circleRadiusInPixels
        ) {
          // Atualiza a posição do círculo vermelho/azul
          a.xPosition = mouseX;
          a.yPosition = mouseY;

          // IMPORTANTE: Garante que círculos vermelhos/azuis não tenham linkedLevelIds
          // Isso evita que eles se conectem com linhas tracejadas
          if (a.linkedLevelIds && a.linkedLevelIds.length > 0) {
            a.linkedLevelIds = []; // Remove todas as conexões
            console.log(`🔴🔵 Removendo conexões do círculo ${a.classification} (${a.id})`);
          }
        }

        // Salva as alterações no serviço
        this._mapPointsService.modify(a);
      }
    });
  }

  /**
   * Lógica de arrasto de pontos (nós do mapa)
   * Implementa limitação rigorosa para evitar qualquer invasão da deadzone
   *
   * Funcionalidades:
   * - Desativado quando o modo de desenho (isShowCanDraw) está ativo
   * - Só permite arrasto quando há um nível selecionado
   * - Aplica limitações rigorosas para evitar invasão da deadzone
   * - Atualiza posição do nível em tempo real durante o arrasto
   */
  private dragPointLogic(mousePos: { x: number; y: number }): void
  {
    // === VERIFICAÇÃO DE PRÉ-REQUISITOS ===
    // Não permite arrasto se o modo de desenho estiver ativo
    if (this.isShowCanDraw) {
      return;
    }

    // Não permite arrasto se não há nível selecionado
    if (!this.selectedLevelId) {
      return;
    }

    // === LIMITAÇÃO RIGOROSA BASEADA NO SISTEMA DE RENDERIZAÇÃO ===
    // O círculo é renderizado em: deadZone + (dimension * percentage / 100)
    // Para evitar invasão, calculamos a porcentagem máxima permitida

    const deadZone = this.unifiedCanvas.deadZoneInPixels;
    const radius = this.unifiedCanvas.circleRadiusInPixels;
    const areaWidth = this.unifiedCanvas.dimension.width;
    const areaHeight = this.unifiedCanvas.dimension.height;

    // === CONVERSÃO DIRETA PARA COORDENADAS RELATIVAS ===
    // Remove deadzone e converte para coordenadas da área útil
    const adjustedX = mousePos.x - deadZone;
    const adjustedY = mousePos.y - deadZone;

    // Converte para porcentagem da área útil
    let percentageX = (adjustedX / areaWidth) * 100;
    let percentageY = (adjustedY / areaHeight) * 100;

    // === CÁLCULO DA MARGEM DE SEGURANÇA ===
    // Margem baseada no raio para garantir que o círculo não invada a deadzone
    const marginX = (radius / areaWidth) * 100;
    const marginY = (radius / areaHeight) * 100;

    // === APLICAÇÃO DOS LIMITES RIGOROSOS ===
    // Garante que o centro do círculo fique dentro dos limites seguros
    percentageX = Math.max(marginX, Math.min(100 - marginX, percentageX));
    percentageY = Math.max(marginY, Math.min(100 - marginY, percentageY));

    // === ATUALIZAÇÃO SEGURA DA POSIÇÃO ===
    if (this.map.points[this.selectedLevelId]) {
      this.map.points[this.selectedLevelId].position.xd = percentageX;
      this.map.points[this.selectedLevelId].position.yd = percentageY;
    }

    // Remove o nível da lista de novos níveis se estiver sendo arrastado
    this.newLevelIds = this.newLevelIds.filter((levelId) => levelId !== this.selectedLevelId);

    if (this.isShowControlPoints) {
      this._drawHermite.initializeBranchesToDraw();
    }

    // === ATUALIZAÇÃO OTIMIZADA DO CANVAS ===
    // Usa método otimizado com throttling para melhor performance durante drag & drop
    this.updateMapCanvasOptimized();
    this._mapService.modify(this.map);
  }

  getMousePos(event, canvas: HTMLCanvasElement): { x: number; y: number }
  {
    const rect = canvas?.getBoundingClientRect();
    return {
      x: event.clientX - rect?.left,
      y: event.clientY - rect?.top,
    };
  }

  /**
   * Obtém a posição do mouse relativa à div de interação
   * Usa a própria div como referência para garantir alinhamento perfeito
   */
  getMousePosFromInteractionLayer(event: MouseEvent): { x: number; y: number }
  {
    // Usa a div de interação como referência direta
    const interactionLayer = event.currentTarget as HTMLElement;
    if (!interactionLayer) {
      return { x: 0, y: 0 };
    }

    const rect = interactionLayer.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    };
  }

  /**
   * @deprecated Método mantido para compatibilidade - usa handleMouseDown
   * @todo see if highlightelement is causing scrolling problems
   */
  selectPoint(event: MouseEvent): void {
    this.handleMouseDown(event);
  }


  selectItem(event : MouseEvent) : void
  {
    if(this.isShowItems)
    {
      this._modalService.itemEvent = this._selectItem.selectItem(this._drawItems.itemEvent,
         this.getMousePos(event, this.itemCanvas.el), this.itemCanvas);

      if(this._modalService.itemEvent.length > 0) this._modalService.canControlModal = !this._modalService.canControlModal;
      else this._modalService.canControlModal = false;
    }
    this.moveTextImagePosition(event);
  }

  /**
   * @deprecated Método mantido para compatibilidade - usa handleMouseUp
   */
  changeCursorStyleDrawMode()
  {
    this.updateCursorForDrawMode();
  }

  /**
   * Detecta qual nível está sob o cursor do mouse
   *
   * Funcionalidades:
   * - Desativado quando o modo de desenho (isShowCanDraw) está ativo
   * - Detecta níveis para hover e seleção
   * - Aplica destaque visual nos níveis detectados
   * - Integra com sistema de destaque no editor lateral
   *
   * @param event Evento do mouse
   * @param select Se true, permite seleção do nível; se false, apenas detecção para hover
   * @returns ID do nível detectado ou undefined
   */
  detectLevel(event: MouseEvent, select?: boolean): string | undefined {
    // === VERIFICAÇÃO DE PRÉ-REQUISITOS ===
    // Não detecta níveis se o modo de desenho estiver ativo
    if (this.isShowCanDraw) {
      return undefined;
    }

    if (!select && event.buttons) return undefined;

    let pointId: string | undefined;
    const mousePos = this.getMousePosFromInteractionLayer(event);

    // ✅ LIMPEZA PRÉVIA: Guarda detecção anterior para comparação
    let previousDetectedId = this.detectedLevelId;

    // Reset da detecção - será definida novamente se mouse estiver sobre algum círculo
    this.detectedLevelId = undefined;
    pointId = undefined;

    // Cria um conjunto de IDs de círculos vermelhos/azuis para exclusão
    const textImageIds = new Set();
    if (this.textPoints && this.textPoints.length > 0) {
      this.textPoints.forEach(point => {
        if (point && point.id) {
          textImageIds.add(point.id);
        }
      });
    }

    this.area.levelIds.forEach((levelId) =>
    {
      // === EXCLUSÃO DE CÍRCULOS VERMELHOS/AZUIS ===
      // Ignora círculos que são textPoints ou imagePoints
      if (textImageIds.has(levelId)) {
        return;
      }

      const point = this.map.points[levelId];
      if (!point) return;

      const positionInPixels = this.editorPort.positionInPixels(point.position);

      if (
        mousePos.x - this.editorPort.deadZoneInPixels >=
          positionInPixels.x - this.editorPort.circleRadiusInPixels &&
        mousePos.x - this.editorPort.deadZoneInPixels <=
          positionInPixels.x + this.editorPort.circleRadiusInPixels &&
        mousePos.y - this.editorPort.deadZoneInPixels >=
          positionInPixels.y - this.editorPort.circleRadiusInPixels &&
        mousePos.y - this.editorPort.deadZoneInPixels <=
          positionInPixels.y + this.editorPort.circleRadiusInPixels &&
        Math.pow
        (
          mousePos.x - positionInPixels.x - this.editorPort.deadZoneInPixels, 2
        ) +
        Math.pow
        (
          mousePos.y - positionInPixels.y - this.editorPort.deadZoneInPixels, 2
        ) <= Math.pow(this.editorPort.circleRadiusInPixels, 2))
        {
          pointId = levelId;
          this.detectedLevelId = pointId;
        }
    });

    // ✅ LIMPEZA DE DESTAQUE HTML: Remove destaque de elementos que não estão mais sob o cursor
    if (previousDetectedId && previousDetectedId !== pointId) {
      const element = document.getElementById(previousDetectedId);
      if (element?.style?.backgroundColor) {
        element.style.backgroundColor = '';
      }
    }

    // ✅ ATUALIZAÇÃO CONDICIONAL: Só redesenha se a detecção mudou
    if (previousDetectedId !== pointId) {
      // Aplica destaque no elemento HTML se há nível detectado
      if (pointId && this._userSettingsService.data.highlightLevelsInEditor) {
        highlightElement(pointId, 1, true, undefined, true);
      }

      // ✅ FORÇA REDESENHO: Atualiza canvas sempre que a detecção mudar
      this.updateMapCanvas();
    }

    return pointId;
  }

  /**
   * Detecta qual textPoint está sob o cursor do mouse
   *
   * Funcionalidades:
   * - Detecta círculos vermelhos (textPoints) e azuis (imagePoints) para hover e seleção
   * - Aplica destaque visual nos textPoints detectados
   * - Integra com sistema de destaque no editor lateral
   * - Funciona apenas quando isShowText ou isShowImage está ativo
   *
   * @param event Evento do mouse
   * @param select Se true, permite seleção do textPoint; se false, apenas detecção para hover
   * @returns ID do textPoint detectado ou undefined
   */
  detectTextPoint(event: MouseEvent, select?: boolean): string | undefined {
    // === VERIFICAÇÃO DE PRÉ-REQUISITOS ===
    // Só detecta textPoints se isShowText ou isShowImage estiver ativo
    if (!this.isShowText && !this.isShowImage) {
      return undefined;
    }

    if (!select && event.buttons) return undefined;

    let textPointId: string | undefined;
    const mousePos = this.getMousePosFromInteractionLayer(event);

    // ✅ LIMPEZA PRÉVIA: Guarda detecção anterior para comparação
    let previousDetectedId = this.detectedTextPointId;

    // Reset da detecção - será definida novamente se mouse estiver sobre algum círculo
    this.detectedTextPointId = undefined;
    textPointId = undefined;

    this.textPoints.forEach((textPoint) => {
      // Calcula posição do textPoint no canvas (mesma lógica do CanvasRenderer)
      const pointPosX = textPoint.xPosition * this.unifiedCanvas.dimension.width * 0.01 + this.unifiedCanvas.deadZoneInPixels;
      const pointPosY = textPoint.yPosition * this.unifiedCanvas.dimension.height * 0.01 + this.unifiedCanvas.deadZoneInPixels;

      // Verifica se o mouse está dentro do círculo do textPoint
      const distance = Math.sqrt(
        Math.pow(mousePos.x - pointPosX, 2) +
        Math.pow(mousePos.y - pointPosY, 2)
      );

      if (distance <= this.unifiedCanvas.circleRadiusInPixels) {
        textPointId = textPoint.id;
        this.detectedTextPointId = textPointId;
      }
    });

    // ✅ LIMPEZA DE DESTAQUE HTML: Remove destaque de elementos que não estão mais sob o cursor
    if (previousDetectedId && previousDetectedId !== textPointId) {
      const element = document.getElementById(previousDetectedId);
      if (element?.style?.backgroundColor) {
        element.style.backgroundColor = '';
      }
    }

    // ✅ ATUALIZAÇÃO CONDICIONAL: Só redesenha se a detecção mudou
    if (previousDetectedId !== textPointId) {
      // Aplica destaque no elemento HTML se há textPoint detectado
      if (textPointId && this._userSettingsService.data.highlightLevelsInEditor) {
        highlightElement(textPointId, 1, true, undefined, true);
      }

      // ✅ FORÇA REDESENHO: Atualiza canvas sempre que a detecção mudar
      this.updateMapCanvas();
    }

    return textPointId;
  }

  /**
   * Destaca um textPoint no mapa quando o mouse passa sobre o elemento HTML
   *
   * Funcionalidades:
   * - Aplica destaque visual no círculo vermelho/azul correspondente no mapa
   * - Integra com sistema de hover bidirecional
   * - Funciona apenas quando isShowText ou isShowImage está ativo
   *
   * @param textPointId ID do textPoint a ser destacado
   * @param highlight Se true aplica destaque, se false remove destaque
   */
  highlightTextPointOnMap(textPointId: string, highlight: boolean): void {
    if (!this.isShowText && !this.isShowImage) return;

    const textPoint = this.textPoints.find(tp => tp.id === textPointId);
    if (!textPoint) return;

    if (highlight) {
      // Aplica destaque visual no círculo do mapa
      // Pode ser implementado alterando a cor ou adicionando um outline
      this.detectedTextPointId = textPointId;
      this.updateMapCanvas(); // Força redesenho para mostrar destaque
    } else {
      // Remove destaque
      if (this.detectedTextPointId === textPointId) {
        this.detectedTextPointId = undefined;
        this.updateMapCanvas(); // Força redesenho para remover destaque
      }
    }
  }

  /**
   * Navega até um textPoint específico no mapa quando clicado no elemento HTML
   *
   * Funcionalidades:
   * - Centraliza a visualização no textPoint selecionado
   * - Destaca temporariamente o textPoint
   * - Funciona apenas quando isShowText ou isShowImage está ativo
   * - Usa a mesma lógica do detectPoint para scroll
   *
   * @param textPointId ID do textPoint para navegar
   */
  navigateToTextPoint(textPointId: string): void {
    if (!this.isShowText && !this.isShowImage) return;

    const textPoint = this.textPoints.find(tp => tp.id === textPointId);
    if (!textPoint) return;

    // Calcula posição do textPoint no canvas (mesma lógica do CanvasRenderer)
    const levelPoint = { xd: textPoint.xPosition, yd: textPoint.yPosition };
    const positionInPixels = this.unifiedCanvas.positionInPixels(levelPoint);

    // Centraliza a visualização no textPoint (mesma lógica do detectPoint)
    this.editorWrapper.scrollLeft = positionInPixels.x;
    this.editorWrapper.scrollTop = positionInPixels.y;

    // Destaca temporariamente o textPoint
    this.detectedTextPointId = textPointId;

    // Atualiza a visualização
    this.updateMapCanvas();
  }

  /**
   * Controla a exibição e ocultação do sistema de desenho sobre o mapa
   *
   * Funcionalidades:
   * - No primeiro clique: ativa o modo de desenho e carrega desenhos salvos do localStorage
   * - No segundo clique: desativa o modo de desenho e oculta os desenhos do canvas
   * - Atualiza o cursor conforme o estado atual
   * - Força atualização do canvas para refletir as mudanças visuais
   *
   * Comportamento esperado:
   * - Primeiro clique: aparecer desenho (se houver no localStorage) ou permitir desenhar
   * - Segundo clique: ocultar desenho completamente do mapa
   */
  canDraw()
  {
    this.isShowCanDraw = !this.isShowCanDraw;

    if(this.isShowCanDraw)
    {
      // Ativa modo de desenho e carrega desenhos salvos do localStorage
      this._drawOnCanvas.getDrawFromLocalStorage(this.unifiedCanvas);
    }
    else
    {
      // Desativa modo de desenho e oculta desenhos
      this.canDeleteDrawSection = false;

      // Força atualização do canvas para ocultar os desenhos
      // O updateMapCanvas() irá redesenhar apenas os elementos necessários
      // sem incluir os desenhos pois isShowCanDraw agora é false
      this.updateMapCanvas();
    }

    // Atualiza o cursor baseado no novo estado
    this.updateCursorForDrawMode();
  }

  /**
   * @deprecated Método mantido para compatibilidade - usa handleMouseMove
   */
  dragPoint(event): void
  {
    this.handleMouseMove(event);
  }

  moveTextImagePosition(event)
  {
    this.selectTextNImagePoint(this.getMousePos(event, this.editorPort.el), this.editorPort.circleRadiusInPixels);
  }

  /**
   * Move a posição do nível com otimização de performance para drag & drop
   * Usa requestAnimationFrame para melhor performance durante movimento
   */
  moveLevelPosition(mousePos)
  {
    //just move the level when the control points are not used/visible. This allow the control point to move.
    if(!this.isShowCanDraw && this.selectedLevelId)
    {
      this.map.points[this.selectedLevelId].position = this.unifiedCanvas.dimension.toAnchorPoint(mousePos);

      // Temporariamente usando método original para debug
      this.updateMapCanvas();
    }
  }

  /**
   * Versão otimizada do updateMapCanvas para operações de drag & drop
   * Implementa throttling inteligente baseado no tamanho do mapa para melhor performance
   */
  private updateMapCanvasOptimized(): void {
    // Se já há uma atualização pendente, não agenda outra
    if (this.pendingUpdate) return;

    this.pendingUpdate = true;

    // Cancela qualquer animationFrame anterior
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    // === THROTTLING INTELIGENTE BASEADO NO TAMANHO DO MAPA ===
    const now = performance.now();

    // Calcula throttling dinâmico baseado na altura do mapa
    let dynamicThrottle = this.UPDATE_THROTTLE_MS;
    if (this.unifiedCanvas?.canvasDimension) {
      const mapHeight = this.unifiedCanvas.canvasDimension.height;
      const mapWidth = this.unifiedCanvas.canvasDimension.width;
      const totalPixels = mapHeight * mapWidth;

      // Aumenta o throttling para mapas muito grandes
      if (totalPixels > 2000000) { // Mapas > 2M pixels
        dynamicThrottle = 50; // 20 FPS máximo
      } else if (totalPixels > 1000000) { // Mapas > 1M pixels
        dynamicThrottle = 33; // 30 FPS máximo
      } else {
        dynamicThrottle = 16; // 60 FPS para mapas menores
      }
    }

    // Agenda a atualização para o próximo frame
    this.animationFrameId = requestAnimationFrame(() => {
      // Implementa throttling adicional para mapas muito grandes
      if (now - this.lastUpdateTime >= dynamicThrottle) {
        this.updateMapCanvas();
        this.lastUpdateTime = now;
      }

      this.pendingUpdate = false;
      this.animationFrameId = null;
    });
  }

  /**
   * Gerencia o desenho e apagamento no canvas
   * - Modo desenho: adiciona novos traços
   * - Modo apagador: remove traços existentes sem afetar outros elementos do mapa
   */
  drawOnCanvas(mousePos: any): void
  {
    if (!this.canDeleteDrawSection && this.isShowCanDraw)
    {
      // Modo desenho: adiciona novos traços
      this._drawOnCanvas.drawOnMouseDown(mousePos);
      return;
    }
    else if(this.canDeleteDrawSection)
    {
      // Modo apagador: remove traços que intersectam com o mouse
      const strokesWereRemoved = this._drawOnCanvas.clearCanvasMousePosition(mousePos);

      // Se traços foram removidos, força redesenho completo do mapa
      // para garantir que todos os elementos (círculos, linhas, etc.) sejam preservados
      if (strokesWereRemoved) {
        this.updateMapCanvas();
      }
    }
  }



  dragViewPort(event: MouseEvent): void 
  {
    if (!this._mousePosViewPort || !event.buttons) return;

    const mousePos = this.getMousePos(event, this.viewPort.el);

    this._viewPortOffsetPosition.x = mousePos.x - this._mousePosViewPort.x;
    this._viewPortOffsetPosition.y = mousePos.y - this._mousePosViewPort.y;

    const dimension = this.mapCanvas.matchDimension(
      this.viewPort.dimension,
      true
    );
    const offsetBoundaries = this.offsetBoundaries(
      dimension,
      this.viewPort.dimension
    );

    if (
      this._viewPortPivot.x + this._viewPortOffsetPosition.x >
      offsetBoundaries.minX
    ) {
      this._viewPortOffsetPosition.x =
        offsetBoundaries.minX - this._viewPortPivot.x;
    } else if (
      this._viewPortPivot.x + this._viewPortOffsetPosition.x <
      offsetBoundaries.maxX
    ) {
      this._viewPortOffsetPosition.x =
        offsetBoundaries.maxX - this._viewPortPivot.x;
    }

    if (
      this._viewPortOffsetPosition.y + this._viewPortPivot.y >
      offsetBoundaries.minY
    ) {
      this._viewPortOffsetPosition.y =
        offsetBoundaries.minY - this._viewPortPivot.y;
    } else if (
      this._viewPortOffsetPosition.y + this._viewPortPivot.y <
      offsetBoundaries.maxY
    ) {
      this._viewPortOffsetPosition.y =
        offsetBoundaries.maxY - this._viewPortPivot.y;
    }

    this.canvasRenderer.updateViewPort(
      this._viewPortPivot,
      this._viewPortOffsetPosition
    );
  }

  getViewPortPivot(event): void 
  {
    this._mousePosViewPort = this.getMousePos(event, this.viewPort.el);
  }
  
  public drawMapOnBackground()
  {
    this.isShowBackground = !this.isShowBackground;
    this.canvasRenderer.showMapInBackground();
    this._drawBackgroundImagesService.drawImageOnBackground(this.area, this.mapCanvas)
    //Forces the update of the map
    this.secondIntervalNum = requestAnimationFrame(()=>{
      this.updateMapCanvas();
    });
  }

  // updates canvas
  updateMapCanvas(): void
  {
    if (!this.canvasRenderer || !this.unifiedCanvas) {
      console.log('⚠️ CanvasRenderer ou unifiedCanvas não disponível');
      return;
    }

    // === ATUALIZA ZOOM NO RENDERER PARA FONTE RESPONSIVA ===
    // Passa o valor atual do zoom para o CanvasRenderer calcular fontes responsivas
    this.canvasRenderer.setZoomValue(this.zoomValue);

    // Desenha o mapa principal com níveis, deadzone, etc.
    this.canvasRenderer.drawMapCanvas(
      this.map,
      this.area,
      this.levelsFromArea,
      this.detectedLevelId,
      this.newLevelIds,
      this.textPoints,
      this.isShowText,
      this.isShowImage,
      this.detectedTextPointId  // ✅ NOVO: TextPoint sob o cursor (hover)
    );

    // Com canvas único, não precisamos mais dos métodos update separados
    // que faziam drawImage de si mesmos
    // this.canvasRenderer.updateEditorPort(this.zoomValue);
    this.canvasRenderer.updateViewPort(this._viewPortPivot, this._viewPortOffsetPosition);
    // this.canvasRenderer.updateDrawCanvas(this.zoomValue);
    // this.canvasRenderer.updateItemCanvas(this.zoomValue);

    // Desenha itens se habilitado
    if(this.isShowItems)
      this._drawItems.drawItemOnCanvas(this.unifiedCanvas);

    // Desenha no canvas se habilitado
    if(this.isShowCanDraw )
    {
      this._drawOnCanvas.getDrawFromLocalStorage(this.unifiedCanvas);
    }

    // Desenha curvas de Hermite se habilitado
    if(this.isShowControlPoints)
      this._drawHermite.initializeBranchesToDraw();

    // Atualiza a camada de interação após mudanças no canvas
    requestAnimationFrame(() => {
      this.setupInteractionLayer();
    });

  }

  generateCirclesPositions() 
  {
    this.textPoints.forEach((a, i) => 
    {
      if (!a.xPosition || !a.yPosition) 
      {
        let remain = (i + 1) % 10 === 0;
        this.x = remain ? 10 : 10 * ((i + 1) % 10);
        this.y = remain ? this.y + 10 : this.y;
        a.xPosition = this.x;
        a.yPosition = this.y;
        this._mapPointsService.promptCreateNewMapPoints(a.id, a.name, a.classification, a.area, a.xPosition, a.yPosition, false);
        this._mapPointsService.modify(a);
      }
    });
    this._mapService.modify(this.map);
  }

  selectTextNImagePoint(mousePos, cirleRadius) 
  {
    if (!this.isMousePressed) this.point = undefined;
    if(this.isShowCanDraw) return;
   
    this.textPoints.forEach((t) => 
    {
      let pointPosX = t.xPosition * this.editorPort.dimension.width * 0.01 + this.editorPort.deadZoneInPixels;
      let pointPosY = t.yPosition * this.editorPort.dimension.height * 0.01 + this.editorPort.deadZoneInPixels;
      if (
        pointPosX - cirleRadius <= mousePos.x &&
        mousePos.x <= pointPosX + cirleRadius &&
        pointPosY - cirleRadius <= mousePos.y &&
        mousePos.y <= pointPosY + cirleRadius
      ) 
      {
        this.scroolToElement(t.id);
        t.isShowText = true;
        this.point = t;
      }
    });  
    this.updateMapCanvas();
  }

  async promptChangeMapRatio(): Promise<void> 
  {
    const aspectRatioString = await Alert.inputText(
      `Enter the height.`,''
    );

    if (!aspectRatioString) return;

    const aspectRatioSplit = aspectRatioString.split(/[xX]/);
    const a = parseFloat(aspectRatioSplit[0]);
    const c = this.viewPort?.ratio?.c;
    //const c = parseFloat(aspectRatioSplit[1]);

    if (!a || !c) 
    {
      Alert.showError('Invalid Aspect Ratio');
      return;
    }
    const newAspectRatio = new Ratio(a, c);
    this._editorService.setMapAspectRatio(this.map, newAspectRatio);
    this.map.aspectRatio = newAspectRatio;
    this.mapCanvas.ratio = newAspectRatio;
    this.editorPort.ratio = newAspectRatio;
    this.redimensionMapCanvas();
    this.updateMapCanvas();
  }

  printMap(): void 
  {
    this.redimensionMapCanvas();
    let version = this._appService.appVersion.tostring();

    this.editorUtilities = new EditorUtilities(this.mapCanvas, this.drawCanvas,
      this.itemCanvas, this.editorPort, this.area, version);

    this.editorUtilities.initializer(this.levelsFromArea, this.map, this._userSettingsService, this.detectedLevelId,
      this.viewPort, this.newLevelIds, this.offsetBoundaries, this.textPoints);

    this.editorUtilities.printMap();

    this.updateMapCanvas();
  }

  public disableControlPoints()
  {
    this.isShowControlPoints = !this.isShowControlPoints;
    this.fourthTimeout = requestAnimationFrame(()=>
    {
      this.updateMapCanvas();
    })
  }

  downloadMap(): void 
  {
    let version = this._appService.appVersion.tostring();

    let editorUtilities = new EditorUtilities(this.mapCanvas, this.drawCanvas, this.itemCanvas, this.editorPort, this.area, version);
    editorUtilities.initializer(this.levelsFromArea, this.map, this._userSettingsService, this.detectedLevelId,
      this.viewPort, this.newLevelIds, this.offsetBoundaries, this.textPoints);

    editorUtilities.downloadMap();

    this.updateMapCanvas();
  }

  showText() 
  {
    this.isShowText = !this.isShowText;
    this.updateMapCanvas();
  }

  showImage() 
  {
    this.isShowImage = !this.isShowImage;
    this.updateMapCanvas();
  }

  showItems() 
  {
    this.isShowItems = !this.isShowItems;
    this.updateMapCanvas();
  }

  scroolToElement(id) 
  {
    if (!this.highlightLevelsInEditor || !this.oldElement) return;
    if (this.oldElement) this.oldElement.style.backgroundColor = '#fff';

    this.element = document.getElementById(id);
    this.oldElement = this.element;

    this.element?.scrollIntoView({behavior: 'smooth', block: 'nearest', inline: 'start'});
    this.element.style.backgroundColor = 'rgb(196,186,255)';
  }

  getColorPicker(curveColor)
  {
    this.currentSelectedColor = curveColor;
    this._drawOnCanvas.setCurveColor(this.currentSelectedColor);
    this._userSettingsService.setCurrentDrawColor(this.currentSelectedColor);
  }

  getCurveThickness(curveThickness : number)
  {
    this.currentCurveThickness = curveThickness;
    this._drawOnCanvas.setCurveThickness(this.currentCurveThickness);
    this._userSettingsService.setCurrentDrawThickness(curveThickness);
  }

  controlLinesThickness(curveThickness)
  {
    this._drawHermite.changeThickness(curveThickness);
    this._userSettingsService.setHermiteCurveThickness(curveThickness);
    this.currentHermiteCurveThickness = curveThickness;
    this.updateMapCanvas();
  }

  controlHermiteTension(curveTension)
  {
    this._drawHermite.changeTension(curveTension);
    this._userSettingsService.setHermiteCurveTension(curveTension);
    this.currentHermiteCurveTension = curveTension;
    console.log('Tension: ' + this.currentHermiteCurveTension);     
    this.updateMapCanvas();
  }

  controlHermiteColor(curveColor:string)
  {
    this._drawHermite.changeColor(curveColor);
    this._userSettingsService.setHermiteCurveColor(curveColor);
    this.updateMapCanvas();
  }

  ngOnDestroy(): void
  {
    if(this.isShowCanDraw)
    {
      this._drawOnCanvas.saveDrawOnLocalStorage();
    }

    this._areaService.runOnce = true;

    // Limpa timeouts
    clearTimeout(this.thridTimeout);
    clearTimeout(this.secondIntervalNum);
    clearTimeout(this.intervalNum);
    clearTimeout(this.editorUtilities?.firsTimeout);
    clearTimeout(this.fourthTimeout);

    // Cancela qualquer animationFrame pendente para evitar vazamentos de memória
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  currentScreenDiagonalSize: number = 29;
  currentImageWidthInch: number = 0;
  currentImageHeightInch: number = 0;
  desiredImageWidthInch : number = 0;
  desiredImageHeightInch: number = 0;
  ratio : {width:number, height:number} = {width:-1, height:-1};
  unit:string = 'Inches';
  desiredPPI : number = null;
  exchangeInch2CM(unit)
  {
    this.unit = unit;
  }

  currentDimensions(diagonal: number)
  {
    if(this.unit == 'Inches')
      this.currentScreenDiagonalSize = diagonal;
    else if(this.unit == 'Centimeters')
      this.currentScreenDiagonalSize = diagonal / 2.54;//One inch = 2.54 cm
    
    let ppi = this._imageRedimensionService.calculateScreenPPI(this.currentScreenDiagonalSize);

    this.currentImageWidthInch = this._imageRedimensionService.
      calculateCurrentImageWidthInch(this._drawBackgroundImagesService.image.width);

    this.currentImageHeightInch = this._imageRedimensionService.
      calculateCurrentImageHeightInch(this._drawBackgroundImagesService.image.height);
  }

  desiredDimensions(ppi:number)
  {
    this.desiredPPI = ppi;
    this.desiredImageWidthInch = this._drawBackgroundImagesService.image.width / ppi;
    this.desiredImageHeightInch = this._drawBackgroundImagesService.image.height / ppi;
  }

  calculateRatios()
  {
    this.ratio = 
    {
      width: this.currentImageWidthInch/this.desiredImageWidthInch, 
      height: this.currentImageHeightInch/this.desiredImageHeightInch
    };
    this.calculateNewImageDimensions();
  }

  calculateNewImageDimensions()
  {
    this.mapCanvas.redimension(
      new PixelDimension(
        this._drawBackgroundImagesService.image.width / this.ratio.width,
        this._drawBackgroundImagesService.image.height / this.ratio.height
      ), false);

    this.updateMapCanvas();
    this.calculateImageZoomPercentage();
  }

  async calculateResolution()
  {
    //This is the value when the percentage is 100%. This is to avoid the image to having differents scales when changing resolution
    this.zoomValue = 79;
    if(this.desiredImageWidthInch == 0 || this.currentScreenDiagonalSize == 0 ||
       this._drawBackgroundImagesService.image.width == 0)
    {
      Alert.showError('', 'Fill the Diagonal Size and Desired PPI fields!');
    }
    else
    {
      await this.currentDimensions(this.currentScreenDiagonalSize);
      await this.desiredDimensions(this.desiredPPI);
      this.calculateRatios();
    }
    this.calculateScreenZoomPercentage();
    this.updateMapCanvas();
  }

  ScreenPercentage: number = 0;
  
  calculateScreenZoomPercentage()
  {
    let imageWidth = this._drawBackgroundImagesService.image.width;
    let screenWidth = window.screen.width;
    //This is to give a percentage of the size the image ocupies in the screen.
    this.ScreenPercentage = (100 * imageWidth) / screenWidth;
  }
  
  ImagePercentage: number = 0;
  calculateImageZoomPercentage()
  {
    if(this._drawBackgroundImagesService.image.width == 0) return;
    this.ImagePercentage = (this.editorPort.canvasDimension.width / this._drawBackgroundImagesService.image.width) * 100;

    this.ImagePercentage = Math.trunc(this.ImagePercentage);
  }

  canShowSelectedDevice: boolean = false;
  canShowDevicesList: boolean = false;
  selectedDevice: Devices = {deviceName:'', devicePPI:0, deviceResolution:'0x0',deviceSize:1, deviceType:'none'};

  showSelectedDevice(device : Devices)
  {
    this.desiredPPI = device.devicePPI;
    this.selectedDevice = device;
    this.desiredDimensions(device.devicePPI);
    this.canShowSelectedDevice = true;
    this.canShowDevicesList = !this.canShowDevicesList;
    this.calculateResolution();
    this.calculateImageZoomPercentage();

  }
    
  showDevicesList()
  {
    this.canShowDevicesList = !this.canShowDevicesList;
    if(this.canShowSelectedDevice)
      this.canShowSelectedDevice = false;
    
    if(!this.canShowSelectedDevice && !this.canShowDevicesList && this.selectedDevice.deviceType != 'none')
      this.canShowSelectedDevice = true;
  }

  async closeSelectedDevice()
  {
    let response = await Alert.confirm('', 'Do you realy want to hide the last selected device', 'OK');
    if(!response) return;
    this.selectedDevice = {deviceName:'', devicePPI:0, deviceResolution:'0x0',deviceSize:1, deviceType:'none'};
    this.canShowSelectedDevice = false;
  }

  // === MÉTODOS PÚBLICOS PARA CONTROLE DOS CÍRCULOS AMARELOS ===

  /**
   * Aumenta o tamanho dos círculos amarelos de itens
   *
   * Este método permite aumentar o raio base dos círculos amarelos
   * que são desenhados pelo método drawItemOnCanvas().
   *
   * @param newRadius Novo raio em pixels (será limitado entre 4 e 30)
   */
  public setYellowCircleSize(newRadius: number): void {
    if (this._drawItems) {
      this._drawItems.setItemCircleBaseRadius(newRadius);

      // Atualiza o canvas se os itens estão sendo mostrados
      if (this.isShowItems) {
        this.updateMapCanvas();
      }

      console.log(`🟡 Tamanho dos círculos amarelos alterado para: ${newRadius}px`);
    }
  }

  /**
   * Define o fator de escala dos círculos amarelos relativo aos círculos dos níveis
   *
   * @param scaleFactor Fator de escala (será limitado entre 0.5 e 3.0)
   */
  public setYellowCircleScaleFactor(scaleFactor: number): void {
    if (this._drawItems) {
      this._drawItems.setItemCircleScaleFactor(scaleFactor);

      // Atualiza o canvas se os itens estão sendo mostrados
      if (this.isShowItems) {
        this.updateMapCanvas();
      }

      console.log(`🟡 Fator de escala dos círculos amarelos alterado para: ${scaleFactor}`);
    }
  }

  /**
   * Obtém o tamanho atual dos círculos amarelos
   *
   * @returns Raio atual em pixels
   */
  public getYellowCircleSize(): number {
    return this._drawItems ? this._drawItems.getItemCircleBaseRadius() : 0;
  }

  /**
   * Obtém o fator de escala atual dos círculos amarelos
   *
   * @returns Fator de escala atual
   */
  public getYellowCircleScaleFactor(): number {
    return this._drawItems ? this._drawItems.getItemCircleScaleFactor() : 0;
  }

  /**
   * Redefine o tamanho dos círculos amarelos para os valores padrão
   */
  public resetYellowCircleSize(): void {
    if (this._drawItems) {
      this._drawItems.resetItemCircleSize();

      // Atualiza o canvas se os itens estão sendo mostrados
      if (this.isShowItems) {
        this.updateMapCanvas();
      }

      console.log('🟡 Tamanho dos círculos amarelos redefinido para padrão');
    }
  }

}
