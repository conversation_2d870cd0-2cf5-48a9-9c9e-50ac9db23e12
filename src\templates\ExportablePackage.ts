import { Versioning } from './Versioning';

export class ExportablePackage<T> extends Versioning {
  // public get length() { return this.data.length + this.removedData.length; }
  public data: T[] = [];
  public removedData?: T[] = [];

  constructor(exportedDateTime?: string, appVersion?: string, data?: T[], removedData?: T[], public nextIndex?: number) {
    super(exportedDateTime, appVersion);
    this.data = data || [];
    this.removedData = removedData || [];
  }
}
