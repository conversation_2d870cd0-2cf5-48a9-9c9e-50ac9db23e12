export namespace Popup 
{
  export class Interface<Type, TargetType> 
  {
    public buttons: Button<Type>[];
    constructor(
      public style: 
      {
        title: string;
        actionsClass: 'default' | 'column' | 'row';
        text?: string;
        buttonSize?: 'lg' | 'sm' | ' ';
      },
      buttons: Button<Type>[],
      public inputSettings?: 
      {
        inputButton?: { value: Type };
        hideButton?: { value: Type };
        next?: (res: Button<Type>) => Interface<any, TargetType>;
      }
    ) 
    {
      if (!style.buttonSize) 
      {
        style.buttonSize = ' ';
      }
      this.buttons = buttons;
      if (inputSettings?.hideButton) 
      {
        this.buttons = buttons.filter(
          (button) => button.value !== inputSettings.hideButton.value
        );
      }
      if (inputSettings?.next) 
      {
        this.buttons = buttons.filter(
          (button) => inputSettings.next(button)?.buttons.length > 0
        );
      }
    }
  }

  export class Button<T> 
  {
    constructor(
      public text: string,
      public value: T,
      public styling?: {
        klass?: string;
        divStyle?: string;
        divText?: string;
      }
    ) 
    {
      if (!styling) 
      {
        this.styling = {};
      }
      if (styling?.klass) 
      {
        this.styling.klass = 'btn-fill btn-' + styling.klass;
      }
    }
  }
}
