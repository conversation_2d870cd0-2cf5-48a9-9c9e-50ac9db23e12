import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs/internal/Subscription';
import { PopupService } from './services/popup.service';

type Theme = 'dark' | 'light' | 'emerald';

/**
 * @component This component uses the <app-route> Angular selector,
 * involving all other components, thus being the main component.
 *
 * @view Wraps the app main panel and sidebar.
 */
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.html'],
})
export class AppComponent implements OnInit, OnDestroy {
  private _poppingSubcription: Subscription;
  public popping: boolean;

  constructor(private _popupService: PopupService) {}

  ngOnDestroy(): void {
    this._poppingSubcription.unsubscribe();
  }
  ngOnInit(): void {
    this._poppingSubcription = this._popupService.popping.subscribe((value) => {
      this.popping = value;
    });
  }
}
