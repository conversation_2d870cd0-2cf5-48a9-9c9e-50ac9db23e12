import { ExportableClass } from './ExportableClass';
import { prefixes } from './EnumMaps';

/**
 * Game Level
 **/
export class Item extends ExportableClass 
{
    id:string;
    name: string;
    description: string;
    tagId: string[] = [];
    gateXP: number; 

    constructor(id: string) 
    {
        super(Item.generateId(id));
    }

    static deepLoad(item: Item): Item 
    {
        return Object.assign(new Item(undefined), item);
    }
    public static generateId(id: string): string 
    {
        return id + '.' + prefixes.ITEM;
    }
}
