import { Canvas } from './Canvas';
import { GetItemEvent } from './GetItemEvent'
import { AnchorPoint, Circle, ItemEvent } from '../../../../models/mapsys1'
import { MapService } from '../../../services/map.service';

interface Position 
{
  x: number;
  y: number;
}

export class DrawItems
{
    public ctx!: CanvasRenderingContext2D;
    canDrawItemOnScreen: boolean = true;
    getItemEvent : GetItemEvent;
    public itemEvent : ItemEvent[] = [];

    // Propriedades para controle dinâmico do tamanho dos círculos amarelos
    private itemCircleBaseRadius: number = 5; // Raio base para círculos de item
    private itemCircleScaleFactor: number = 1.2; // Fator de escala relativo aos círculos dos níveis

    constructor(private _drawCanvas: Canvas, private _mapService : MapService)
    {
        this._drawCanvas = _drawCanvas;
        this.ctx = this._drawCanvas.context;
        this.initializeCTXValues();

        this.getItemEvent = new GetItemEvent(this._mapService);
    }
    
    /**
     * Desenha círculos amarelos de itens no canvas com tamanho dinâmico
     *
     * O tamanho dos círculos é calculado dinamicamente baseado no tamanho
     * dos círculos dos níveis para manter proporção visual adequada.
     *
     * @param editorPort Canvas onde desenhar os círculos
     */
    public drawItemOnCanvas(editorPort : Canvas)
    {
        this.itemEvent = this.getItemEvent.initializeItemEvent();
        let circle : Circle = new Circle();

        // === CÁLCULO DINÂMICO DO RAIO ===
        // Calcula o raio baseado no tamanho dos círculos dos níveis
        const dynamicRadius = this.calculateDynamicRadius(editorPort);

        for(let i = 0; i < this.itemEvent.length; i++)
        {
            let anchor : AnchorPoint = {xd : 0, yd: 0};

            //25 and 1 are arbitrary numbers to put the item in 45º.
            anchor.xd = this.itemEvent[i].levelXPosition + 25;
            anchor.yd = this.itemEvent[i].levelYPosition + 1;

            let position : Position = editorPort.positionInPixels(anchor, false);
            circle.x = position.x;
            circle.y = position.y;
            circle.radius = dynamicRadius; // Usa o raio calculado dinamicamente

            this.drawCircle(circle);
        }
    }

    /**
     * Calcula o raio dinâmico para os círculos amarelos baseado no canvas atual
     *
     * O raio é calculado considerando:
     * - O raio dos círculos dos níveis (se disponível)
     * - O fator de escala configurado
     * - Um valor mínimo e máximo para garantir boa visibilidade
     *
     * @param editorPort Canvas atual
     * @returns Raio calculado em pixels
     */
    private calculateDynamicRadius(editorPort: Canvas): number
    {
        let calculatedRadius = this.itemCircleBaseRadius;

        // === CÁLCULO BASEADO NO RAIO DOS CÍRCULOS DOS NÍVEIS ===
        if (editorPort.circleRadiusInPixels) {
            // Usa o raio dos círculos dos níveis como referência
            calculatedRadius = editorPort.circleRadiusInPixels * this.itemCircleScaleFactor;
        } else if (editorPort.levelRadius) {
            // Fallback: usa levelRadius se circleRadiusInPixels não estiver disponível
            calculatedRadius = editorPort.levelRadius * this.itemCircleScaleFactor;
        }

        // === LIMITAÇÃO DE TAMANHO ===
        // Garante que o círculo não fique muito pequeno nem muito grande
        const minRadius = 3;  // Raio mínimo para boa visibilidade
        const maxRadius = 18; // Raio máximo para não dominar a tela

        return Math.max(minRadius, Math.min(maxRadius, Math.floor(calculatedRadius)));
    }
    
    /**
     * Desenha um círculo amarelo de item com sombra e bordas
     *
     * O círculo agora usa raio dinâmico calculado baseado no tamanho
     * dos círculos dos níveis para manter proporção visual adequada.
     *
     * @param circle Objeto Circle com posição (x, y) e raio dinâmico
     * @param fillColor Cor de preenchimento (padrão: amarelo)
     * @param strokeColor Cor da borda (padrão: dourado escuro)
     */
    private drawCircle(circle : Circle, fillColor : string = 'yellow', strokeColor : string = '#fcd303')
    {
        this.ctx.beginPath();

        // === CONFIGURAÇÃO DA SOMBRA ===
        this.ctx.shadowColor = '#333';
        this.ctx.shadowBlur = 4;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;

        // === DESENHO DO CÍRCULO COM RAIO DINÂMICO ===
        this.ctx.arc(circle.x, circle.y, circle.radius, 0, 2 * Math.PI, false);
        this.ctx.fillStyle = fillColor;
        this.ctx.fill();
        this.ctx.strokeStyle = strokeColor;
        this.ctx.stroke();

        // === LIMPEZA DO EFEITO DE SOMBRA ===
        this.ctx.shadowBlur = 0;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;
    }
    
    private initializeCTXValues()
    {
        this.ctx.lineWidth = 3;
        this.ctx.lineCap = 'round';
    }

    // === MÉTODOS PÚBLICOS PARA CONTROLE DO TAMANHO ===

    /**
     * Define o raio base para os círculos amarelos de itens
     *
     * @param radius Novo raio base em pixels (será limitado entre 4 e 30)
     */
    public setItemCircleBaseRadius(radius: number): void
    {
        this.itemCircleBaseRadius = Math.max(4, Math.min(30, radius));
    }

    /**
     * Define o fator de escala relativo aos círculos dos níveis
     *
     * @param factor Fator de escala (será limitado entre 0.5 e 3.0)
     */
    public setItemCircleScaleFactor(factor: number): void
    {
        this.itemCircleScaleFactor = Math.max(0.5, Math.min(3.0, factor));
    }

    /**
     * Obtém o raio base atual dos círculos amarelos
     *
     * @returns Raio base em pixels
     */
    public getItemCircleBaseRadius(): number
    {
        return this.itemCircleBaseRadius;
    }

    /**
     * Obtém o fator de escala atual
     *
     * @returns Fator de escala atual
     */
    public getItemCircleScaleFactor(): number
    {
        return this.itemCircleScaleFactor;
    }

    /**
     * Redefine os valores para os padrões
     */
    public resetItemCircleSize(): void
    {
        this.itemCircleBaseRadius = 6;
        this.itemCircleScaleFactor = 1.2;
    }
}