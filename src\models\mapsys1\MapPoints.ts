import { ExportableClass } from './ExportableClass';
import { prefixes } from './EnumMaps';

export class MapPoints extends ExportableClass 
{
  constructor(public id:string, index: number,public name,public classification,public area,public xPosition,public yPosition, public isShowText) 
  {
    super(MapPoints.generateId(index));
  }

  static deepLoad(level: MapPoints): MapPoints 
  {
    const deepLoadedMap = Object.assign(
      new MapPoints(undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined), level);
    deepLoadedMap.xPosition = level.xPosition
    deepLoadedMap.yPosition = level.yPosition
    return deepLoadedMap;
  }

  public static generateId(index: number): string {
    return  prefixes.MAP_POINTS + index;
  }
}
