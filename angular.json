{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mapsys": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/assets/scss/bootstrap-dashboard-theme.scss"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/bootstrap/dist/js/bootstrap.js", "node_modules/bootstrap-notify/bootstrap-notify.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "development": {"optimization": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "mapsys:build:development"}, "configurations": {"production": {"browserTarget": "mapsys:build:production"}, "development": {"browserTarget": "mapsys:build:development", "port": 42100}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "mapsys:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/assets/scss/bootstrap-dashboard-theme.scss"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/bootstrap/dist/js/bootstrap.js", "node_modules/bootstrap-notify/bootstrap-notify.js"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "mapsys:serve"}, "configurations": {"production": {"devServerTarget": "mapsys:serve:production"}}}}}}, "defaultProject": "mapsys", "cli": {"analytics": false}}