import { ExportableClass } from './ExportableClass';
import { prefixes } from './EnumMaps';

/**
 * Game Area
 **/
export class Area extends ExportableClass 
{
  public description: string;
  public hierarchyCode: string;
  public levelIds: string[] = [];

  constructor(index: number, public name: string) 
  {
    super(Area.generateId(index));
  }

  static deepLoad(area: Area): Area
  {
    let newArea : Area = new Area(undefined, undefined);
    newArea.levelIds = area.levelIds;
    return Object.assign(newArea, area);
  }

  public static generateId(index: number): string 
  {
    return prefixes.AREA + index;
  }

  public static getSubIdFrom(otherId: string): string 
  {
    return this.getSubId(this.generateId(0), otherId);
  }
}
