import { ExportableClass } from './ExportableClass';
import { prefixes } from './EnumMaps';


export class DSAEvent extends ExportableClass 
{
    type: string;
    itemId:string;
    videoId:string;
    amount:string;
    tutorialId:string;
    missionId:string;

    constructor(index: number, areaId: string) 
    {
        super(DSAEvent.generateId(areaId, index));
    }

    static deepLoad(event: DSAEvent) : DSAEvent 
    {
        let auxEvent = Object.assign(new DSAEvent(undefined, undefined), event);
        return auxEvent;
    }
    public static generateId(areaId: string, index: number): string 
    {
        return areaId + '.' + prefixes.DSA_EVENT + index;
    }
}