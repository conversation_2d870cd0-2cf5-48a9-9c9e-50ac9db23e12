import { Pipe, PipeTransform } from '@angular/core';
import { Area } from '../../models/mapsys1';
import { MapPointsService } from '../services/map-points.service';

@Pipe({
  name: 'MapTextMapped',
})
export class MapTextMappedPipe implements PipeTransform {
  constructor(private _mapPointsService : MapPointsService){}

  transform(area: Area): number 
  {
    let levelsMapped = 0;
    this._mapPointsService.data.forEach(t=> 
    {
      if(t.area?.split(':')[1]?.split(' ').join('').trim() === area.name?.split(' ').join(''))
      {
        if(t.isShowText === false || !t.isShowText)
        {
          levelsMapped++
        }
      }
    }) 
   
    return levelsMapped;
  }
}
