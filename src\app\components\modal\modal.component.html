<!--
  MODAL DINÂMICO COM SCROLL INTELIGENTE

  Este modal ajusta automaticamente seu tamanho baseado no número de itens:
  - 1 item: Modal compacto (altura mínima)
  - 2-3 itens: Modal pequeno
  - 4-6 itens: Modal médio
  - 7+ itens: Modal grande

  O scroll é aplicado APENAS na tabela quando necessário, não no modal inteiro.

  As classes são calculadas dinamicamente pelos métodos:
  - getModalSizeClass(): Tamanho do modal
  - getTableContainerClass(): Container da tabela com scroll
  - getTableSizeClass(): Estilo da tabela
  - getRowSizeClass(): Altura das linhas
-->
<div class="modal-backdrop"></div>
<div>
    <!-- Modal com classe dinâmica baseada no número de itens -->
    <dialog [class]="getModalSizeClass()">
        <!-- Botão de fechar fixo no topo -->
        <div style="position: absolute; right: 10px; top: 10px; cursor: pointer; z-index: 1000;">
            <i class="pe-7s-close i-icon btn-size" (click)="closeModal()"></i>
        </div>
        <p>Items from the levels</p>

        <!-- Container com scroll apenas para a tabela -->
        <div class="table-container" [class]="getTableContainerClass()">
            <!-- Tabela com classe dinâmica para ajuste de altura -->
            <table id="customers" [class]="getTableSizeClass()">
                <thead>
                <tr class="w3-light-grey">
                    <th>Item ID</th>
                    <th>Item Name</th>
                    <th>Item Type</th>
                </tr>
                </thead>
                <tbody>
                    <!-- Linhas com altura dinâmica baseada no número de itens -->
                    <tr *ngFor="let event of this._modalService.itemEvent" [class]="getRowSizeClass()">
                    <td>{{event.itemID}}</td>
                    <td>{{event.itemName}}</td>
                    <td>{{event.itemType}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </dialog>
</div>