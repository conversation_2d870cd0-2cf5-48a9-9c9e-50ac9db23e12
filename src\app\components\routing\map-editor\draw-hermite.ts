import { Canvas } from './Canvas';
import { CalculateLevelBranches } from './calculateLevelBranches'

interface Tangent
{
    x:number;
    y:number;
}

export class DrawHermite
{
    canDraw : boolean = true;
    points : [number, number][] = []; 
    response : [number, number][] = []; 
    lineFromDSA : [string, number, number][] = [];
    controlPoints : [string, number, number][] = [];
    lineToDraw : [number, number][] = [];
    numberOfSegments : number = 15;
    step : number = 0;
    cardinals : number[] = [];
    tension : number = 0.3;
    public context!: CanvasRenderingContext2D;
    runOnce : boolean = false;
    intersections : number[] = [];
    linesToDraw = [];
    calculateLevelBranches : CalculateLevelBranches;
    lineThickness : number = 1;
    lineColor : string = 'red';

    constructor(private editorPort : Canvas, private mapPoints, private levelsFromArea)
    {
        this.context = this.editorPort.context;
        this.calculateLevelBranches = new CalculateLevelBranches(mapPoints, levelsFromArea, editorPort);
    }

    private initializeContextVariables()
    {
        this.context.strokeStyle = this.lineColor;
    }

    public changeTension(tension)
    {
        this.tension = tension;
    }

    public changeColor(lineColor)
    {
        this.lineColor = lineColor;
    }

    public changeThickness(lineThickness)
    {
        this.lineThickness = lineThickness;
    }

    public initializeBranchesToDraw()
    {
        this.linesToDraw = this.calculateLevelBranches.initializeBranchesToDraw(this.linesToDraw, this.lineToDraw);
        this.drawCurves();
    }
   
    //This uses Hermite interpolation. All the theory is in the documentation. Look for Hermite.
    private calculateCurvePoints (lineToDraw, tension) 
    {
        this.canDraw = false;
        this.points = lineToDraw.slice(0);

        this.points.unshift(lineToDraw[0]);
        this.points.push(lineToDraw[lineToDraw.length-1]);

        let tangentOne : Tangent = {x:0, y:0};
        let tangentTwo : Tangent = {x:0, y:0};

        for (let i=1; i < (this.points.length - 2); i++) 
        {
            for (let t=0; t <= this.numberOfSegments; t++) 
            { 
                // calc tension vectors
                tangentOne.x = (this.points[i+1][0] - this.points[i-1][0]) * tension;
                tangentTwo.x = (this.points[i+2][0] - this.points[i][0]) * tension;

                tangentOne.y = (this.points[i+1][1] - this.points[i-1][1]) * tension;
                tangentTwo.y = (this.points[i+2][1] - this.points[i][1]) * tension;

                // calc step
                this.step = t / this.numberOfSegments;

                // calc cardinals
                this.cardinals.push(2 * Math.pow(this.step, 3) - 3 * Math.pow(this.step, 2) + 1); 
                this.cardinals.push(-(2 * Math.pow(this.step, 3)) + 3 * Math.pow(this.step, 2)); 
                this.cardinals.push(Math.pow(this.step, 3) - 2 * Math.pow(this.step, 2) + this.step); 
                this.cardinals.push(Math.pow(this.step, 3) - Math.pow(this.step, 2));

                // calc x and y cords with common control vectors
                let x = this.cardinals[0] * this.points[i][0] + this.cardinals[1] * this.points[i+1][0] + 
                    this.cardinals[2] * tangentOne.x + this.cardinals[3] * tangentTwo.x;

                let y = this.cardinals[0] * this.points[i][1] + this.cardinals[1] * this.points[i+1][1] + 
                    this.cardinals[2] * tangentOne.y + this.cardinals[3] * tangentTwo.y;

                //store points in array
                this.response.push([x,y]);
                this.cardinals = [];
            }
        }
        this.canDraw = true;

        return this.response;
    }

    private drawLines(context, points) 
    {
        if(points.length == 0) return;
        context.beginPath();
            context.lineWidth = this.lineThickness;
            context.moveTo(points[0][0], points[0][1]);
            for(let i=1;i<points.length;i++)
            {
                context.lineTo(points[i][0], points[i][1]);
                //Close the curve. Still need to add the segment in the last segment
            /*  if(i == points.length-1)
                    context.lineTo(points[0][0], points[0][1]); */
            }
        context.stroke();
        return points;
    }

    private drawCurves()
    {
        this.initializeContextVariables();
        for(let i = 0; i < this.linesToDraw.length; i++)
        {
            this.drawCurve(this.context, this?.linesToDraw[i], this.tension);
        }
    }

   private drawCurve(context, lineToDraw, tension) 
    { 
        if(!this.canDraw) return
            this.drawLines(context, this.calculateCurvePoints(lineToDraw, tension));
            this.response = [];
    }
}

