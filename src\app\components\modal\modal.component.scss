//Modal
/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}

.modal-close	
{	
    justify-content: flex-end;	
}	

.btn-size {
  font-size: 45px;
  color: red;
}

.btn-size:hover {
  color: orange;
}

dialog
{
    display:flex;
    flex-direction: column;
    top:20%;
    width: 50%;
    height: 50%;
    border-radius: 10px;
    overflow: hidden; /* Remove scroll do modal principal */
    z-index: 999;
    border: 2px solid grey;
    padding: 15px;
    box-sizing: border-box;
}

/* === CLASSES DINÂMICAS PARA AJUSTE DE TAMANHO === */

/* === CLASSES DINÂMICAS PARA AJUSTE DE TAMANHO DO MODAL === */

/* Modal compacto para 1 item */
dialog.modal-compact {
    height: auto;
    min-height: 250px; /* Altura mínima ajustada */
    max-height: 350px;
}

/* Modal pequeno para 2-3 itens */
dialog.modal-small {
    height: auto;
    min-height: 300px;
    max-height: 450px;
}

/* Modal médio para 4-6 itens */
dialog.modal-medium {
    height: auto;
    max-height: 550px;
}

/* Modal grande para 7+ itens */
dialog.modal-large {
    height: auto;
    min-height: 500px;
    max-height: 80vh; /* Usa viewport height para não ultrapassar a tela */
}

/* === CONTAINER COM SCROLL PARA A TABELA === */
.table-container {
  flex: 1;
  overflow-y: auto; /* Scroll vertical apenas quando necessário */
  overflow-x: hidden; /* Evita scroll horizontal desnecessário */
  max-height: calc(100% - 60px); /* Reserva espaço para header e padding */
  border-radius: 5px;
  /* Estilização da barra de scroll */
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.table-container::-webkit-scrollbar {
  width: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

#customers {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 0; /* Remove margem inferior */
  margin-top: 0; /* Remove margem superior (controlada pelo container) */
}

#customers td, #customers th {
  border: 1px solid #ddd;
  padding: 8px;
}

#customers tr:nth-child(even){background-color: #f2f2f2;}

#customers tr:hover {background-color: #ddd;}

#customers th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: grey;
  color: white;
}

/* === CLASSES DINÂMICAS PARA ALTURA DAS LINHAS === */

/* === AJUSTES PARA CONTAINER DE TABELA COMPACTA === */
.table-container.table-compact {
  max-height: 200px; /* Altura máxima para tabelas compactas */
  margin-top: 35px; /* Margem reduzida para modal compacto */
}

.table-container.table-small {
  max-height: 300px; /* Altura máxima para tabelas pequenas */
}

.table-container.table-medium {
  max-height: 400px; /* Altura máxima para tabelas médias */
}

.table-container.table-large {
  max-height: 500px; /* Altura máxima para tabelas grandes */
}

/* Tabela compacta para poucos itens */
#customers.table-compact tr {
  height: auto;
  min-height: 40px;
}

#customers.table-compact td {
  padding: 6px 8px;
  vertical-align: middle;
}

/* Linhas compactas para 1 item */
tr.row-compact {
  height: 45px !important;
}

/* Linhas pequenas para 2-3 itens */
tr.row-small {
  height: 50px !important;
}

/* Linhas normais para 4+ itens */
tr.row-normal {
  height: 55px !important;
}

p {
 font-size: 20px;
 font-weight: 600;
 align-self: center;
}

/* === MELHORIAS VISUAIS PARA MODAL COM CONTAINER === */

/* Ajustes específicos para modal compacto com 1 item */
dialog.modal-compact {
  justify-content: flex-start;
  align-items: stretch;
}

/* Ajustes para modal pequeno */
dialog.modal-small {
  justify-content: flex-start;
  align-items: stretch;
}

/* Ajustes para modal médio */
dialog.modal-medium {
  justify-content: flex-start;
  align-items: stretch;
}

/* Ajustes para modal grande */
dialog.modal-large {
  justify-content: flex-start;
  align-items: stretch;
}

/* === RESPONSIVIDADE PARA TELAS MENORES === */
@media (max-width: 768px) {
  dialog.modal-compact {
    width: 80%;
    min-height: 220px;
    max-height: 300px;
  }

  dialog.modal-small {
    width: 85%;
    min-height: 270px;
    max-height: 400px;
  }

  dialog.modal-medium {
    width: 90%;
    min-height: 350px;
    max-height: 500px;
  }

  dialog.modal-large {
    width: 95%;
    min-height: 450px;
    max-height: 70vh;
  }

  /* Ajustes do container da tabela para mobile */
  .table-container.table-compact {
    max-height: 150px;
    margin-top: 30px;
  }

  .table-container.table-small {
    max-height: 200px;
    margin-top: 35px;
  }

  .table-container.table-medium {
    max-height: 300px;
    margin-top: 40px;
  }

  .table-container.table-large {
    max-height: 350px;
    margin-top: 40px;
  }
}

/* === ANIMAÇÃO SUAVE PARA TRANSIÇÕES === */
dialog {
  transition: height 0.3s ease, min-height 0.3s ease, max-height 0.3s ease;
}

.table-container {
  transition: max-height 0.3s ease, margin-top 0.3s ease;
}

#customers {
  transition: margin 0.3s ease;
}

tr {
  transition: height 0.2s ease;
}

/* === MELHORIAS DE ACESSIBILIDADE === */
.table-container:focus-within {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Melhora a visibilidade do scroll em temas escuros */
@media (prefers-color-scheme: dark) {
  .table-container::-webkit-scrollbar-track {
    background: #2d2d2d;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: #555;
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    background: #777;
  }
}



.textColor
{
    color:blue;
    
}
.card
{
    background-color:  rgb(212, 212, 212);
    display: flex;
    flex-direction: row;
    gap:50px;
    align-content: space-between;
}

button
{
    align-items: center;
    gap: 10px 50px;
}

h3
{
    color:blue;
    font-size: 80%;
}

h2
{
    font-size: 90%;
    font-weight: bold;
}

.column
{
     flex-direction: column;
    align-items: center;
    gap: 10px 50px;
}


