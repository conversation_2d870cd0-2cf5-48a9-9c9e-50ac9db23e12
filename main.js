const { app, BrowserWindow } = require('electron');
const { electron } = require('process');
const contextMenu = require('electron-context-menu');

contextMenu({
  labels:{}
});

let win;

function createWindow() {

  // Create the browser window.
  win = new BrowserWindow({
    webPreferences: {
      spellcheck: true
    },
    width: 1240,
    height: 800,
    backgroundColor: '#ffffff',
    //icon: `file://${__dirname}/dist/assets/logo.png`
  })

  win.maximize();


  win.loadURL(`file://${__dirname}/dist/index.html`);

  //// uncomment below to open the DevTools.
  // win.webContents.openDevTools()

  // Event when the window is closed.
  win.on('closed', function () {
    win = null
  })

  return win;
}

// Create window on electron intialization
app.on('ready', createWindow)

// Quit when all windows are closed.
app.on('window-all-closed', function () {

  // On macOS specific close process
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', function () {
  // macOS specific close process
  if (win === null) {
    createWindow()
  }
})
