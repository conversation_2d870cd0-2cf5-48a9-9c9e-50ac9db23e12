import { Injectable } from '@angular/core';
import { Level, LevelPoint, Ratio } from 'src/models/mapsys1';
import { AreaService } from './area.service';
import { LevelService } from './level.service';
import { MapService } from './map.service';
import { UserSettingsService } from './user-settings.service';

import { Map } from 'src/models/mapsys1';
interface MapSettings
{
  viewPortPivot: { x: number; y: number }; 
  zoomValue: number;
}

@Injectable({
  providedIn: 'root'
})
export class EditorService {

  constructor(
    public areaService: AreaService,
    public mapService: MapService,
    public userSettingsService: UserSettingsService,
    public levelService: LevelService) { }

  
  newMap(areaId: string)
  {
    this.areaService.sortLevels(areaId);
    let userSettings : MapSettings = this.userSettingsService.getEditorConfigurations(areaId)
       
    const map = this.mapService.promptCreateNewMapPoints(
      areaId, 
      new Ratio(userSettings.viewPortPivot.x, userSettings.viewPortPivot.y) 
    );
    const radius = this.userSettingsService.data.levelDiameter * 0.5;
    const levelIds = this.areaService.findById(areaId).levelIds;
    
    const levelsPerPage = 10;

    const pages = levelIds.length / levelsPerPage;

    const minHeight = 16;
    const modularHeight = minHeight * (pages * 0.5); 
    const size : Ratio = new Ratio(
      (modularHeight > minHeight) ? modularHeight : minHeight,
      9
    );

    map.aspectRatio = size;

    this.userSettingsService.getEditorConfigurations(map.id).zoomValue = 50;

    let position = new LevelPoint({xd: 0, yd: 100});

    let pageItems = 0;

    levelIds.forEach(level => {

      let xMultiplier = (position.position.xd == 0) ? 1 : 0;
      let yMultiplier = (position.position.yd == 100) ? -1 : 0;

      let xOffset = 90/(levelsPerPage/2);

      map.points[level] = new LevelPoint({
        xd:
          position.position.xd + radius * xMultiplier,
        yd:
          position.position.yd + (radius/1.75) * yMultiplier
      });
      pageItems ++;

      if(pageItems <= levelsPerPage * 0.5)
      {
        position.position.xd += (xOffset);
      }
      if(pageItems > levelsPerPage * 0.5)
      {
        position.position.xd -= (xOffset);
      }
      if(pageItems >= levelsPerPage)
        pageItems = 0;

      position.position.yd -= (10/pages);
    });

    this.mapService.add(map);
  }

  getLevelParent(levelId: string)
  {
    for(let i = 0; i < this.levelService.data.length; i++){
      let localLevel = this.levelService.data[i];
      if(localLevel.linkedLevelIds.includes(levelId))
      {
        return localLevel;
      }
    } 
  }

  clampMap(map: Map)
  {
    const radius = this.userSettingsService.data.levelDiameter * 0.5

    const minRange = 0;
    const maxRange = 100;

    let points = map.points;
    let levels = this.levelService.filterByLocation(map.areaId);

    levels.forEach(level => {
      let point = points[level.id];

      if(point.position.xd >= maxRange)
      {
        point.position.xd = maxRange - radius;
      }
      else if(point.position.xd <= minRange)
      {
        point.position.xd = minRange + radius;
      }
      if(point.position.yd >= maxRange)
      {
        point.position.yd = maxRange - radius;
      }
      else if(point.position.yd <= minRange)
      {
        point.position.yd = minRange + radius;
      }
    });

    this.mapService.modify(map);
  }

  createMapPoint(map: Map, levelid:string, pointPosition?:LevelPoint)
  {

    let parentLevel = this.getLevelParent(levelid);
    let parentLevelId = (parentLevel)? parentLevel.id : '';
    let parentLevelPosition = (parentLevelId)? map.points[parentLevelId].position : {xd:0,yd:0};

    const offset = 2;
    let newLevelPosition = {xd: parentLevelPosition.xd + offset, yd: parentLevelPosition.yd + offset};

    map.points[levelid] = (pointPosition)? pointPosition : new LevelPoint(newLevelPosition);

    this.mapService.modify(map);
  }

  removeMapPoint(map: Map, levelId: string)
  {
    map.points[levelId] = undefined;
    this.mapService.modify(map);
  }

  setMapAspectRatio(map:Map, aspectRatio:Ratio)
  {
    map.aspectRatio = aspectRatio;
    this.mapService.modify(map);
  }
}