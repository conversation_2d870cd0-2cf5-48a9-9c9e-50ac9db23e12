import { Component, OnInit } from '@angular/core';
import { Alert } from 'src/custom/Alert';
import { File } from 'src/custom/File';
import { AppService } from 'src/app/services/app.service';
import { Subscription } from 'rxjs/internal/Subscription';
import { DataService } from 'src/app/services/data.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { AreaService } from 'src/app/services/area.service';
import { LevelService } from 'src/app/services/level.service';

interface UnityData
{
  _exportedDateTime : string;
  _appVersion: string;
  fileFormat: string;
  MapsNPoints;
  MapPivotNZoom;
  hermiteCurveTension:string;
}

interface ScreenDraws 
{
  mapName: string;
  data: string;
}

@Component({
  selector: 'app-mps-file-menu',
  templateUrl: './mps-file-menu.component.html',
  styleUrls: ['./mps-file-menu.component.scss'],
})
export class MPSFileMenuComponent implements OnInit {
  appSubscription: Subscription;
  lastLoadedTime: Date;
  screenDraws : ScreenDraws[] = [];

  constructor(
    private _appService: AppService,
    private _dataService: DataService,
    private _userSettings : UserSettingsService,
    private _areaService : AreaService,
    private _levelService: LevelService
  ) {}

  ngOnInit(): void 
  {

    this.lastLoadedTime = this._appService.data.MPS_lastLoadedTime;
    this.appSubscription = this._appService.lastMPSLoadedTimeSubject.subscribe(
      (value) => { this.lastLoadedTime = value; }
    );
    this.getScreenDraws();
  }

  ngOnDestroy(): void 
  {
    this.appSubscription.unsubscribe();
  }

  setScreenDraws(jsonString: string) : string
  {
    let screenDraws = JSON.parse(jsonString).ScreenDraws;
    if(screenDraws)
    {
      for(let i = 0; i < screenDraws.length; i++)
      {
        localStorage.setItem(screenDraws[i].mapName, screenDraws[i].data);
      }
    }

    return JSON.stringify(jsonString);
  }


  promptImportMPS(files): void 
  {
    const file = files[0];
    const fileReader = new FileReader();
    fileReader.onload = (error) => 
    {
      this.loadMPS(fileReader.result);
    };
    fileReader.readAsText(file)
  }
  
  private loadMPS(json): void 
  {
    this.setScreenDraws(json);
    let mps: File.MPS1 = new File.MPS1(json);
    let validation : Error = mps.validationCheck();
    if(validation)
    {
      Alert.showError(validation);
      return;
    }

    this._dataService.importMps(mps);

    Alert.showSuccess(
      'Data imported',
      '<h6>Version</h6>' + mps.fileFormat,
      1000,
      'top-end'
    );
  }

  getScreenDraws() : ScreenDraws[]
  {
    for(let i = 0; i < this._areaService.data.length; i++)
    {
      let data = localStorage.getItem(this._areaService.data[i].name)
      if(data)
      {
        let screenDraw : ScreenDraws = {mapName: this._areaService.data[i].name, data:data}
        this.screenDraws.push(screenDraw);
      }
    }

    return this.screenDraws;
  }

  downloadMPS(): void 
  {
    let currentDate = new Date();
    let exportedDateTime = currentDate.toLocaleDateString() + '_' + currentDate.toLocaleTimeString();
    const file = this._dataService.exportMps();
    file['ScreenDraws'] = this.getScreenDraws();
    const anchorElement = document.createElement('a');
    const blob = new Blob([JSON.stringify(file, null, 2)], 
    {
      type: 'text/plain',
    });
    anchorElement.href = window.URL.createObjectURL(blob);
    anchorElement.download = exportedDateTime + '.' + file.fileFormat;
    anchorElement.click();
  }

  exportToUnity()
  {
    let currentDate = new Date();
    let exportedDateTime = currentDate.toLocaleDateString() + '_' + currentDate.toLocaleTimeString();
    const file = this._dataService.exportMps();
    let hermiteTension = this._userSettings.getHermiteCurveTension();

    let unityData : UnityData = 
    {
      _exportedDateTime: exportedDateTime,
      _appVersion: file[Object.keys(file)[1]],
      fileFormat: 'unityBezierData',
      hermiteCurveTension: hermiteTension.toString(),
      MapsNPoints: JSON.parse(localStorage.getItem('Map.mps1.data')),
      MapPivotNZoom : file.userSettings.editorConfigurations
    }
   
    const anchorElement = document.createElement('a');
    const blob = new Blob([JSON.stringify(unityData, null, 2)], 
    {
      type: 'text/plain',
    });

    anchorElement.href = window.URL.createObjectURL(blob);
    anchorElement.download = exportedDateTime + '.' + 'unityBezierData';
    anchorElement.click();
  }
}
