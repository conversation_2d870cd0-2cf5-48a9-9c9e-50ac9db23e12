<!-- Navbar -->
<div class="content">
  <div class="card" style="height: 100%">
    <div class="card-header">
      <h2>Preferences</h2>
    </div>
    <div class="row card-body">
      <div class="col-auto">
        <div class="row">
          <div class="col-auto">
            <label class="label">Level Diameter (%)</label>
            <input
              class="form-control form-short"
              type="number"
              min="1"
              max="100"
              [value]="levelDiameter"
              (change)="setLevelDiameter(levelDiameterInput.value)"
              #levelDiameterInput
            />
          </div>
        </div>
        <div class="row">
          <div class="col-auto">
            <label class="label">Dead Zone Size (%)</label>
            <input
              class="form-control form-short"
              type="number"
              min="1"
              max="100"
              [value]="deadZoneSize"
              (change)="changeDeadZoneSize(deadZoneSizeInput.value)"
              #deadZoneSizeInput
            />
          </div>
        </div>
      </div>
      <div class="col-auto">
        <div class="phone-frame">
          <div class="phone-top">
            <div class="phone-notch"></div>
          </div>
   
          <div class="col" style="margin: -6px">
            <canvas id="map-visualizer"></canvas>
          </div>
     
            <div class="phone-bottom">
          <div class="home-button"></div>
        </div>
        </div>
      </div>
      <div class="col-auto">
        <app-view-port-aspect-ratio-list (preferencesWindowWidth)="preferencesWidth($event)" [updateMapVisualizer]="updateMapVisualizerFunc"></app-view-port-aspect-ratio-list>
      </div>
    </div>
    <div class="card-header">
      <h2>Data</h2>
    </div>
    <div class="row card-body">
      <div class="col-auto">
        Check DSA Data from Local Storage (Recommended)
        <div class="category">
          When this option is enabled, it saves space from local storage.
          <br />
          Unchecking this requires to load DSA data manually through the DSA
          import button.
        </div>
      </div>
      <div class="col-auto">
        <div class="btn-group btn-group-toggle" data-toggle="buttons">
          <label
            id="toggle-true"
            [ngClass]="
              detectDSADataFromLocalStorage | detectDSAToggleButtonStyle: true
            "
          >
            <input type="radio" (click)="promptToggleDSADetection(true)" />
            <span class="d-none d-sm-block d-md-block d-lg-block d-xl-block"
              >Yes</span
            >
            <span class="d-block d-sm-none">
              <i class="tim-icons icon-single-02"></i>
            </span>
          </label>
          <label
            id="toggle-false"
            [ngClass]="
              detectDSADataFromLocalStorage | detectDSAToggleButtonStyle: false
            "
          >
            <input type="radio" (click)="promptToggleDSADetection(false)" />
            <span class="d-none d-sm-block d-md-block d-lg-block d-xl-block"
              >No</span
            >
            <span class="d-block d-sm-none">
              <i class="tim-icons icon-gift-2"></i>
            </span>
          </label>
        </div>
      </div>
    </div>
    <div class="row card-body">
      <div class="col-auto">
        Clear App Data
        <div class="category">This resets the MapSys' local data.</div>
      </div>
      <div class="col-auto">
        <button class="btn btn-danger btn-fill" (click)="promptClearAppData()">
          Clear
        </button>
      </div>
    </div>
  </div>
  <div class="card-header card-title mg-preferences">
    <div class="row">
      <div class="col-auto">
        <h3 class="card-title">
          Local Storage ({{ localStorageSizeInKB.total | thousandNumberFormat }}
          KB)
        </h3>
      </div>
    </div>
    <div class="row">
      <div class="col-auto">
        <button class="btn btn-danger" (click)="promptClearAllData()">X</button>
      </div>
      <div class="col-auto">
        <p class="category">
          Clear All Data From Local Storage<br />
          This resets all data, including data from other apps that uses the
          local storage, such as DSAdmin. Use with caution.
        </p>
      </div>
    </div>
  </div>
  <div class="card-body mg-preferences" style="height: auto; overflow-y: scroll">
    <div class="col-sm-12">
      <table class="table table-storage">
        <thead>
          <th>Project Data</th>
          <th>Path</th>
          <th class="td-kb">Size</th>
        </thead>
        <tr>
          <td>Others</td>
          <td>unknown</td>
          <td class="td-kb">
            {{ localStorageSizeInKB.others | thousandNumberFormat }} KB
          </td>
        </tr>
        <tr>
          <td>MPS (MapSys)</td>
          <td>*{{ MPS_SUFFIX_PATH }}</td>
          <td class="td-kb">
            {{ localStorageSizeInKB.mpsTotal | thousandNumberFormat }}
            KB
          </td>
        </tr>
        <ng-container *ngFor="let mpsPath of mpsDataPaths">
          <tr class="category">
            <td></td>
            <td>
              {{ mpsPath }}
            </td>
            <td class="td-kb">
              {{ localStorageSizeInKB.mps[mpsPath] | thousandNumberFormat }}
              KB
            </td>
          </tr>
        </ng-container>
        <tr>
          <td>DSA (DSAdmin)</td>
          <td>*{{ DSA_SUFFIX_PATH }}</td>
          <td class="td-kb">
            {{ localStorageSizeInKB.dsaTotal | thousandNumberFormat }}
            KB
          </td>
        </tr>
        <ng-container *ngFor="let dsaPath of dsaDataPaths">
          <tr class="category">
            <td></td>
            <td>
              {{ dsaPath }}
            </td>
            <td class="td-kb">
              {{ localStorageSizeInKB.dsa[dsaPath] | thousandNumberFormat }}
              KB
            </td>
          </tr>
        </ng-container>
      </table>
    </div>
  </div>
</div>
