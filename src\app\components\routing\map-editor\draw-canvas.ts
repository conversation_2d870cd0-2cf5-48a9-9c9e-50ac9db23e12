import { Canvas } from './Canvas';
import { SaveDrawsController } from './save-draws-controller';
import { DrawCanvasDeleteController } from './draw-canvas-delete-controller';
import { DrawSaveModel } from './draw-save-model';

interface mousePosition 
{
  x: number;
  y: number;
}

export class CanvasForDrawingOnScreen 
{
  public ctx!: CanvasRenderingContext2D;
  previousMousePosition: mousePosition = { x: 0, y: 0 };
  canDrawOnScreen: boolean = true;
  canDeleteDrawOnScreen: boolean = false;
  saveDrawsController: SaveDrawsController;
  currentCurveDensity : number = 0.1;
  canVerifyAgain : boolean = true;
  drawCanvasDeleteController : DrawCanvasDeleteController;

  constructor(private mapName: string, private drawCanvas: Canvas) 
  {
    this.mapName = mapName;
    this.drawCanvas = drawCanvas;
    this.ctx = this.drawCanvas.context;
    this.saveDrawsController = new SaveDrawsController();
    this.drawCanvasDeleteController = new DrawCanvasDeleteController(this.canDrawOnScreen, this.canDeleteDrawOnScreen, this.ctx,
       this.saveDrawsController, this.mapName, this.canVerifyAgain);

    this.ctx.lineWidth = 5;
    this.ctx.lineCap = 'round';
    this.ctx.strokeStyle = '#000';
    this.drawCanvasDeleteController.deleteTrashNumbers();
  }

  public getPreviousMousePosition(mousePosition: mousePosition) 
  {
    this.previousMousePosition = mousePosition;
  }

  public drawOnCanvasWhenMousePressed(mousePosition: mousePosition, editorPort : Canvas, 
    currentSelectedColor : string, currentCurveThickness : number) 
  {
    if (!this.ctx) return;

    if ((mousePosition.x != this.previousMousePosition.x || 
        mousePosition.y != this.previousMousePosition.y) &&
        (Math.abs(mousePosition.x - this.previousMousePosition.x) > this.currentCurveDensity ||
        Math.abs(mousePosition.y - this.previousMousePosition.y) > this.currentCurveDensity)) 
    {
      this.ctx.beginPath();
      this.ctx.lineWidth = currentCurveThickness;
      this.ctx.moveTo(this.previousMousePosition.x, this.previousMousePosition.y);
      this.ctx.lineTo(mousePosition.x, mousePosition.y);
      this.ctx.stroke();
      this.previousMousePosition.x = mousePosition.x;
      this.previousMousePosition.y = mousePosition.y;

      this.saveDrawsController.addPointOnSavePointArray(this.mapName, 
        {x:mousePosition.x, y:mousePosition.y,
          dimensionX:editorPort.dimension.width,
          dimensionY: editorPort.dimension.height,
          color: currentSelectedColor, thickness: currentCurveThickness});
    }
  }

  public drawOnCanvasFromLocalStorage() 
  {
   /*  if (!this.ctx) return;
    
    let drawSaveModel : DrawSaveModel = this.saveDrawsController.loadPointsFromLocalStorage(this.mapName);

    if(!drawSaveModel || !drawSaveModel?.drawPoints || drawSaveModel?.drawPoints.length <= 0) return;

    for(let i = 0; i < drawSaveModel.drawPoints.length; i++)
    {
      //Verify when a line starts. For more info go to documentation look for: verify line start.
      if(drawSaveModel.drawPoints[i]?.x == -1 || drawSaveModel.drawPoints[i+1]?.x == -1) continue; 
      if(i == drawSaveModel.drawPoints.length - 1) break;
      this.ctx.beginPath();
        this.ctx.lineCap = 'round';
        this.ctx.strokeStyle = drawSaveModel.drawPoints[i].color == undefined ? 'blue' : drawSaveModel.drawPoints[i].color; 
        this.ctx.lineWidth = drawSaveModel.drawPoints[i].thickness == undefined ? 5 : drawSaveModel.drawPoints[i].thickness;
        this.ctx.moveTo(drawSaveModel.drawPoints[i].x, drawSaveModel.drawPoints[i].y);
        this.ctx.lineTo(drawSaveModel.drawPoints[i+1].x, drawSaveModel.drawPoints[i+1].y);
      this.ctx.stroke();
    } */
  }
  
  public verifyMousePositionOverDrawPoint(mousePosition)
  {
    /* if(!this.canVerifyAgain) return;
    let auxPositions : DrawSaveModel = this.saveDrawsController.loadPointsFromLocalStorage(this.mapName);
    if(!auxPositions || !auxPositions?.drawPoints || auxPositions?.drawPoints?.length <= 0) return;
    let offset : number = 5;
     for(let i = 0; i < auxPositions.drawPoints.length; i++)
    {
      if(mousePosition.x > auxPositions.drawPoints[i].x - offset && 
        mousePosition.x < auxPositions.drawPoints[i].x + offset &&
        mousePosition.y > auxPositions.drawPoints[i].y - offset &&
        mousePosition.y < auxPositions.drawPoints[i].y + offset)
        {
          
          this.canVerifyAgain = false;
          this.canVerifyAgain = this.drawCanvasDeleteController.deleteDrawSegment(auxPositions, i);
          break;
        }
      } */
    }
}
